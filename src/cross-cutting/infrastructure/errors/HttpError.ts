import { isObject } from '@discocil/fv-domain-library/domain';

import type { HTTP_CODES } from '@app/http/HttpCodes';
import type { HttpBodyParams, HttpParams } from '../contracts/HttpParams';

type ErrorBody = Record<string, string>;

export class HttpError extends Error {
  private constructor(
    readonly url: URL,
    readonly status: HTTP_CODES,
    readonly bodyParams: HttpBodyParams | undefined,
    message: string,
  ) {
    super(message);
  }

  static build(params: HttpParams, status: HTTP_CODES, errorBody: ErrorBody): HttpError {
    const { url, bodyParams } = params;

    const exceptionMessage = this.makeMessage(errorBody);

    return new this(url, status, bodyParams, exceptionMessage);
  }

  private static makeMessage(errorBody: ErrorBody): string {
    let exceptionMessage!: string;

    if (isObject(errorBody)) {
      exceptionMessage = errorBody.message || errorBody.error || 'Http Unexpected Error';
    }

    const lastIndex = exceptionMessage.indexOf('. Reasons');

    if (lastIndex > -1) {
      exceptionMessage = exceptionMessage.substring(0, exceptionMessage.indexOf('. Reasons'));
    }

    return exceptionMessage;
  }

  toString(): string {
    let exceptionMessage = `HttpError: ${this.message}. Path: ${this.url.pathname}`;

    if (this.url.searchParams.size > 0) {
      exceptionMessage = `${exceptionMessage}. Query params: ${this.url.searchParams}`;
    }

    if (this.bodyParams) {
      exceptionMessage = `${exceptionMessage}. Body Params: ${JSON.stringify(this.bodyParams)}`;
    }

    return exceptionMessage;
  }
}
