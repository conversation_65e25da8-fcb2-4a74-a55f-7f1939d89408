import { MongoCriteriaConverter } from '@discocil/fv-criteria-converter-library/infrastructure';

import { EDBNames } from '../../enums/DBNames';

import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type mongoose from 'mongoose';
import type { Schema } from 'mongoose';
import type { DatabaseConnection } from './MongoConnection';

export abstract class MongoRepository {
  constructor(protected readonly connection: DatabaseConnection) {}

  protected readonly criteriaConverter = new MongoCriteriaConverter(this.propertiesMapper());

  protected abstract getSchema(): Schema;
  protected abstract getModel(): string;

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected propertiesMapper(): Record<string, string> {
    return {
      id: '_id',
      removedAt: 'removed_at',
    };
  }

  protected async getConnection(model?: string, schema?: Schema): Promise<typeof mongoose.Model> {
    model ||= this.getModel();
    schema ||= this.getSchema();

    const database = this.connection.useDb(this.getDBName());

    await this.connection.logServerStatus();

    const dbModel = database.models[model];

    return dbModel ?? database.model(model, schema);
  }

  async count(criteria: Criteria): Promise<number> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    return connection.countDocuments(filterQuery.filter);
  }
}
