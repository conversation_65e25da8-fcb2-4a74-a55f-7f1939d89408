import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { CommonFieldKeys } from './CommonFields';

class FilterField extends FilterFieldBase<CommonFieldKeys> {}

export class RemovedAtFilter {
  private static readonly field: CommonFieldKeys = 'removedAt';

  static buildRemoved(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.distinct();
    const filterValue = FilterValue.buildZero();

    return new Filter(field, operator, filterValue);
  }

  static buildActive(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.buildZero();

    return new Filter(field, operator, filterValue);
  }
}
