import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';

import { FeatureFlag } from '@/featureFlags/domain/entities/FeatureFlag';

import type { FeatureFlagEither } from '@/featureFlags/domain/entities/FeatureFlag';
import type { FeatureFlagsSchemaType } from '../schemas/FeatureFlagsSchemaType';

export class FeatureFlagMapper {
  static execute(data: FeatureFlagsSchemaType): FeatureFlagEither {
    return this.buildEntity(data);
  }

  static buildEntity(data: FeatureFlagsSchemaType): FeatureFlagEither {
    const startDate = data.start_unix ? FvDate.createFromMilliSeconds(data.start_unix).toPrimitive() : null;
    const endDate = data.end_unix ? FvDate.createFromMilliSeconds(data.end_unix).toPrimitive() : null;

    return FeatureFlag.build({
      id: data._id,
      uid: data.uid,
      key: data.key,
      description: Maybe.fromValue(data.description),
      organizationIds: new Set(data.organizations_ids ?? []),
      organizationSlugs: new Set(data.organizations_slugs ?? []),
      organizationCountries: new Set(data.organizations_countries ?? []),
      organizationTiers: new Set(data.organizations_tiers ?? []),
      userSlugs: new Set(data.users_slugs ?? []),
      userPermissions: new Set(data.users_permissions ?? []),
      environment: data.environment,
      percentage: data.percentage,
      status: data.status,
      startDate: Maybe.fromValue(startDate),
      endDate: Maybe.fromValue(endDate),
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}
