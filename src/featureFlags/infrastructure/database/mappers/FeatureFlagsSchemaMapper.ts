import { FvDate } from '@discocil/fv-domain-library/domain';

import type { FeatureFlag } from '@/featureFlags/domain/entities/FeatureFlag';
import type { FeatureFlagsSchemaType } from '../schemas/FeatureFlagsSchemaType';

export class FeatureFlagsSchemaMapper {
  static execute(featureFlag: FeatureFlag): FeatureFlagsSchemaType {
    return {
      _id: featureFlag.id,
      uid: featureFlag.uid,
      key: featureFlag.key,
      description: featureFlag.description.getOrElse(''),
      organizations_ids: Array.from(featureFlag.organizationIds),
      organizations_slugs: Array.from(featureFlag.organizationSlugs),
      organizations_countries: Array.from(featureFlag.organizationCountries),
      organizations_tiers: Array.from(featureFlag.organizationTiers),
      users_slugs: Array.from(featureFlag.userSlugs),
      users_permissions: Array.from(featureFlag.userPermissions),
      environment: featureFlag.environment,
      percentage: featureFlag.percentage,
      status: featureFlag.status,
      start_unix: featureFlag.startDate.fold(() => null, date => FvDate.create(date).toMilliseconds()),
      end_unix: featureFlag.endDate.fold(() => null, date => FvDate.create(date).toMilliseconds()),
      created_at: featureFlag.createdAt,
      created_by: featureFlag.createdBy,
      updated_at: featureFlag.updatedAt,
      updated_by: featureFlag.updatedBy,
      removed_at: featureFlag.removedAt,
      removed_by: featureFlag.removedBy,
    };
  }
}
