export const featureFlagsSchema = {
  _id: { type: String },
  uid: { type: String },
  key: { type: String },
  description: { type: String },
  organizations_tiers: { type: [String] },
  organizations_ids: { type: [String] },
  organizations_slugs: { type: [String] },
  organizations_countries: { type: [String] },
  users_slugs: { type: [String] },
  users_permissions: { type: [String] },
  environment: { type: String },
  percentage: { type: Number },
  status: { type: String },
  start_unix: { type: Number },
  end_unix: { type: Number },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: { type: Number },
  removed_by: { type: String },
};
