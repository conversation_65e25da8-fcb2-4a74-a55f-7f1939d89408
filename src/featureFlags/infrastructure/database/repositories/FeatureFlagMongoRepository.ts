import {
  Paginator, type Criteria, type RequiredCriteria,
} from '@discocil/fv-criteria-converter-library/domain';
import {
  left, NotFoundError, right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { FeatureFlag } from '@/featureFlags/domain/entities/FeatureFlag';

import { FeatureFlagMapper } from '../mappers/FeatureFlagMapper';
import { featureFlagsSchema } from '../schemas/FeatureFlagsSchema';

import type { FeatureFlagsRepository } from '@/featureFlags/domain/contracts/FeatureFlagsRepository';
import type {
  FeatureFlagEither,
  FeatureFlagKeys,
  FeatureFlags,
  SearchFeatureFlagsEither,
} from '@/featureFlags/domain/entities/FeatureFlag';
import type { FeatureFlagsSchemaType } from '../schemas/FeatureFlagsSchemaType';

type PropertiesMapper = Partial<Record<FeatureFlagKeys, keyof FeatureFlagsSchemaType>>;

export class FeatureFlagMongoRepository extends MongoRepository implements FeatureFlagsRepository {
  protected getSchema(): Schema {
    return new Schema(featureFlagsSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'featuresflags';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      key: 'key',
      environment: 'environment',
      organizationSlugs: 'organizations_slugs',
      userSlugs: 'users_slugs',
      removedAt: 'removed_at',
      status: 'status',
      startDate: 'start_unix',
      endDate: 'end_unix',
    };
  }

  async find(criteria: Criteria): Promise<FeatureFlagEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const resultFind = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<FeatureFlagsSchemaType>();

    return resultFind
      ? FeatureFlagMapper.execute(resultFind)
      : left(NotFoundError.build({ context: this.constructor.name, target: FeatureFlag.name }));
  }

  async search(criteria: Criteria): Promise<SearchFeatureFlagsEither> {
    const response: FeatureFlags = new Map();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter)
      .sort(filterQuery.sort)
      .lean<FeatureFlagsSchemaType[]>();

    if (queryResponse.length === 0) {
      return right({ featureFlags: new Map() });
    }

    for (const _featureFlag of queryResponse) {
      const featureFlagResult = FeatureFlagMapper.execute(_featureFlag);

      if (featureFlagResult.isLeft()) {
        return left(featureFlagResult.value);
      }

      const featureFlag = featureFlagResult.value;

      response.set(_featureFlag._id.toString(), featureFlag);
    }

    if (criteria.pagination) {
      const total = await connection.countDocuments(filterQuery.filter);
      const requiredCriteria = criteria as RequiredCriteria;

      return right({
        featureFlags: response,
        pagination: Paginator.execute(total, requiredCriteria, queryResponse.length),
      });
    }

    return right({ featureFlags: response });
  }
}
