import { PerformanceMeasurementDecorator } from '@discocil/fv-domain-library/application';
import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { FindFeatureFlagOverridesUseCase } from '@/featureFlags/application/FindFeatureFlagOverridesUseCase';
import { FindFeatureFlagUseCase } from '@/featureFlags/application/FindFeatureFlagUseCase';
import { FeatureFlagsDependencyIdentifier } from '@/featureFlags/domain/dependencyIdentifier/FeatureFlagsDependencyIdentifier';
import { OrganizationDependencyIdentifier } from '@/organizations/organizations/domain/dependencyIdentifier/OrganizationDependencyIdentifier';
import { UserDependencyIdentifier } from '@/user/domain/dependencyIdentifier/UserDependencyIdentifier';

import { FeatureFlagMongoRepository } from '../database/repositories/FeatureFlagMongoRepository';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { Logger } from '@discocil/fv-domain-library/domain';
import type { DependencyContainer } from 'tsyringe';

export const FeatureFlagContainer = {
  register: (): void => {
    container.register(FeatureFlagsDependencyIdentifier.FeatureFlagsRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new FeatureFlagMongoRepository(dbConnection);
      }),
    });

    container.register(FindFeatureFlagUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new FindFeatureFlagUseCase(
          container.resolve(UserDependencyIdentifier.UserRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          container.resolve(FeatureFlagsDependencyIdentifier.FeatureFlagsRepository),
        );

        const overrideUseCase = new FindFeatureFlagOverridesUseCase(useCase);

        return new PerformanceMeasurementDecorator(
          FindFeatureFlagUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          overrideUseCase,
        ) as T;
      },
    });
  },
};
