import { left, right } from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';

import type { UseCase } from '@discocil/fv-domain-library/application';
import type { FindFeatureFlagDto } from '../domain/contracts/FindFeatureFlagDto';
import type { FindMicrositeResponse } from '../domain/contracts/FindFeatureResponse';
import type { FindFeatureFlagUseCase } from './FindFeatureFlagUseCase';

export class FindFeatureFlagOverridesUseCase implements UseCase<FindFeatureFlagDto, Promise<FindMicrositeResponse>> {
  constructor(
    private readonly useCase: FindFeatureFlagUseCase,
  ) {}

  @contextualizeError()
  async execute(dto: FindFeatureFlagDto): Promise<FindMicrositeResponse> {
    const { slugChannel, featureFlagKey } = dto;

    const useCaseResultOrError = await this.useCase.execute(dto);

    if (useCaseResultOrError.isLeft()) {
      return left(useCaseResultOrError.value);
    }

    const useCaseResult = useCaseResultOrError.value;

    const { isOrganization } = useCaseResult;
    let { isEnabledForSlugs, organizationSlugs } = useCaseResult;

    // Temporal fix
    // organization slugs are all the organizations the user belongs to
    // this is overriden to only self
    // with this we do not check if all the user organizations have the flag enabled
    // and instead just check for the current user
    if (!isOrganization) {
      organizationSlugs = new Set([slugChannel]);
    }

    // Temporal fix
    // old cli should be disabled for non-organization users
    if (!isOrganization && featureFlagKey === 'oldcli') {
      isEnabledForSlugs = false;
    }

    return right({
      ...useCaseResult,
      organizationSlugs,
      isEnabledForSlugs,
    });
  }
}
