import {
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { FeatureFlagsCriteriaMother } from '../domain/filters/FeatureFlagsCriteriaMother';
import { GetOrganizationSlugsFromUser } from '../domain/services/GetOrganizationSlugsFromUser';

import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UserRepository } from '@/user/domain/contracts/UserRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { FeatureFlagsRepository } from '../domain/contracts/FeatureFlagsRepository';
import type { FindFeatureFlagDto } from '../domain/contracts/FindFeatureFlagDto';
import type { FindMicrositeResponse } from '../domain/contracts/FindFeatureResponse';

export class FindFeatureFlagUseCase implements UseCase<FindFeatureFlagDto, Promise<FindMicrositeResponse>> {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly featureFlagRepository: FeatureFlagsRepository,
  ) {}

  @contextualizeError()
  async execute(dto: FindFeatureFlagDto): Promise<FindMicrositeResponse> {
    const {
      slugChannel, featureFlagKey, environment,
    } = dto;

    const organizationCriteria = OrganizationCriteriaMother.slugToMatch(slugChannel);
    const organizationResultOrError = await this.organizationRepository.find(organizationCriteria);

    const isOrganization = organizationResultOrError.isRight();

    const organizationSlugs = new Set<string>([slugChannel]);

    if (!isOrganization) {
      organizationSlugs.clear();

      const getOrganizationSlugsFromUser = new GetOrganizationSlugsFromUser(this.userRepository, this.organizationRepository);
      const organizationSlugsOrError = await getOrganizationSlugsFromUser.execute(slugChannel);

      if (organizationSlugsOrError.isLeft()) {
        return left(organizationSlugsOrError.value);
      }

      const userOrganizationSlugs = organizationSlugsOrError.value;

      if (userOrganizationSlugs.size > 0) {
        for (const organizationSlug of userOrganizationSlugs) {
          organizationSlugs.add(organizationSlug);
        }
      }
    }

    const criteria = FeatureFlagsCriteriaMother.organizationSlugsToMatch(featureFlagKey, environment);

    const featureFlagResult = await this.featureFlagRepository.find(criteria);

    if (featureFlagResult.isLeft()) {
      return left(featureFlagResult.value);
    }

    const featureFlag = featureFlagResult.value;

    const isEnabledForSlugs = featureFlag.isEnabledForSlugs(organizationSlugs);

    return right({
      featureFlag,
      organizationSlugs,
      isEnabledForSlugs,
      isOrganization,
    });
  }
}
