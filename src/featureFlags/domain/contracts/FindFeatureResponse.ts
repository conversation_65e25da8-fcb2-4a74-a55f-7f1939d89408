
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { BadSlugError } from '@/microsite/domain/errors/BadSlugError';
import type {
  Either, NotFoundError, UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { FeatureFlag } from '../entities/FeatureFlag';

export type FindFeatureFlagResponseType = {
  readonly featureFlag: FeatureFlag;
  readonly organizationSlugs: Set<string>;
  readonly isEnabledForSlugs: boolean;
  readonly isOrganization: boolean;
};

export type FindMicrositeResponse = Either<
  UnexpectedError
  | MapperError
  | NotFoundError
  | BadSlugError,
  FindFeatureFlagResponseType
>;
