import {
  AggregateRoot,
  Collection,
  FvDate,
  FvNumber,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';


import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import { FeatureFlagStatus } from '../value-objects/FeatureFlagStatus';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  CreatedAt,
  CreatedBy,
  DatePrimitive,
  Either,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  Properties,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';
import type { EFeatureFlagStatuses } from '../value-objects/FeatureFlagStatus';

export const enum EFeatureFlagEnvironments {
  LOCAL = 'local',
  ALPHA = 'alpha',
  PRODUCTION = 'production'
}

export type FindFeatureFlagEither = Either<MapperError | NotFoundError, FeatureFlag>;

export type FindFeatureFlagResponse = {
  paginatedFeatureFlags: SearchPaginatedFeatureFlags;
};

export type FeatureFlagPrimitives = Primitives<
  Omit<
    FeatureFlag,
    'organizationTiers'
    | 'userPermissions'
    | 'organizationSlugs'
    | 'organizationIds'
    | 'organizationCountries'
    | 'userSlugs'
    | 'userPermissions'
    | 'status'
    | 'startDate'
    | 'endDate'
  >
> & {
  organizationTiers: Set<string>;
  organizationIds: Set<string>;
  organizationSlugs: Set<string>;
  organizationCountries: Set<string>;
  userSlugs: Set<string>;
  userPermissions: Set<string>;
  status: string;
  startDate: Maybe<DatePrimitive>;
  endDate: Maybe<DatePrimitive>;
};

export type FeatureFlags = Map<IdPrimitive, FeatureFlag>;

export type SearchPaginatedFeatureFlags = PaginationMetadataResponse & {
  readonly featureFlags: FeatureFlags;
};

export type FeatureFlagsPaginated = Pick<SearchPaginatedFeatureFlags, 'featureFlags' | 'pagination'>;

export type FeatureFlagEither = Either<MapperError | NotFoundError, FeatureFlag>;
export type FeatureFlagsEither = Either<MapperError | NotFoundError, FeatureFlagsPaginated>;

export type SearchFeatureFlagsEither = Either<MapperError | NotFoundError, SearchPaginatedFeatureFlags>;

export type FeatureFlagKeys = keyof Properties<FeatureFlag>;

export class FeatureFlag extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    readonly uid: string,
    readonly key: string,
    readonly description: Maybe<string>,
    private readonly _organizationTiers: Collection<string>,
    private readonly _organizationIds: Collection<string>,
    private readonly _organizationSlugs: Collection<string>,
    private readonly _organizationCountries: Collection<string>,
    private readonly _userSlugs: Collection<string>,
    private readonly _userPermissions: Collection<string>,
    readonly environment: string,
    readonly percentage: number,
    private readonly _status: FeatureFlagStatus,
    private readonly _startDate: Maybe<FvDate>,
    private readonly _endDate: Maybe<FvDate>,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: FeatureFlagPrimitives): FeatureFlagEither {
    const id = UniqueEntityID.build(primitives.id);

    const description = primitives.description.map(item => item);

    const organizationTiers = Collection.build(primitives.organizationTiers);
    const organizationIds = Collection.build(primitives.organizationIds);
    const organizationSlugs = Collection.build(primitives.organizationSlugs);
    const organizationCountries = Collection.build(primitives.organizationCountries);
    const userSlugs = Collection.build(primitives.userSlugs);
    const userPermissions = Collection.build(primitives.userPermissions);

    const startDate = primitives.startDate.map(item => FvDate.create(item));
    const endDate = primitives.endDate.map(item => FvDate.create(item));

    const stamps = stampValueObjects(primitives);

    const entity = new FeatureFlag(
      id,
      primitives.uid,
      primitives.key,
      description,
      organizationTiers,
      organizationIds,
      organizationSlugs,
      organizationCountries,
      userSlugs,
      userPermissions,
      primitives.environment,
      primitives.percentage,
      FeatureFlagStatus.build(primitives.status),
      startDate,
      endDate,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get status(): EFeatureFlagStatuses {
    return this._status.value;
  }

  get organizationTiers(): Set<string> {
    return new Set(this._organizationTiers.toArray());
  }

  get organizationIds(): Set<string> {
    return new Set(this._organizationIds.toArray());
  }

  get organizationSlugs(): Set<string> {
    return new Set(this._organizationSlugs.toArray());
  }

  get organizationCountries(): Set<string> {
    return new Set(this._organizationCountries.toArray());
  }

  get userSlugs(): Set<string> {
    return new Set(this._userSlugs.toArray());
  }

  get userPermissions(): Set<string> {
    return new Set(this._userPermissions.toArray());
  }

  get startDate(): Maybe<DatePrimitive> {
    return this._startDate.map(item => item.toPrimitive());
  }

  get endDate(): Maybe<DatePrimitive> {
    return this._endDate.map(item => item.toPrimitive());
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  isEnabledForSlugs(slugs: Set<string>): boolean {
    const isEnabled = this.isEnabled();
    const isForAll = this.isForAllStatus();

    if (isEnabled && !isForAll) {
      return this.hasEveryOrganizationSlug(slugs);
    }

    return isEnabled || isForAll;
  }

  private getIsActiveAtCurrentDate(): boolean {
    if (this._startDate.isEmpty() && this._endDate.isEmpty()) {
      return true;
    }

    if (this._startDate.isDefined()) {
      if (this._startDate.get().isFuture()) {
        return false;
      }
    }

    if (this._endDate.isDefined()) {
      if (this._endDate.get().isPast()) {
        return false;
      }
    }

    return true;
  }

  private hasEveryOrganizationSlug(organizationSlugs: Set<string>): boolean {
    if (organizationSlugs.size === 0) {
      return false;
    }

    for (const _slug of organizationSlugs) {
      if (!this._organizationSlugs.contains(_slug)) {
        return false;
      }
    }

    return true;
  }

  private isEnabled(): boolean {
    if (!this.getIsActiveAtCurrentDate()) {
      return false;
    }

    if (!this.checkPercentage()) {
      return false;
    }

    return this._status.isActive();
  }

  private isForAllStatus(): boolean {
    return this._status.isForAll();
  }

  private checkPercentage(): boolean {
    const percentage = FvNumber.build(this.percentage);

    if (percentage.isZero()) {
      return false;
    }

    if (percentage.isEqualTo(1)) {
      return true;
    }

    if (percentage.isLessThan(1)) {
      return FvNumber.random().isGreaterThan(percentage);
    }

    return false;
  }

  toPrimitives(): FeatureFlagPrimitives {
    return {
      id: this.id,
      uid: this.uid,
      key: this.key,
      description: this.description,
      organizationTiers: this.organizationTiers,
      organizationIds: this.organizationIds,
      organizationSlugs: this.organizationSlugs,
      organizationCountries: this.organizationCountries,
      userSlugs: this.userSlugs,
      userPermissions: this.userPermissions,
      environment: this.environment,
      percentage: this.percentage,
      status: this._status.toPrimitive(),
      startDate: this.startDate,
      endDate: this.endDate,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
