import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { EnvironmentFilter } from './EnvironmentFilter';
import { KeyFilter } from './KeyFilter';
import { OrganizationSlugFilter } from './OrganizationSlugFilter';

import type { EFeatureFlagEnvironments } from './../entities/FeatureFlag';

export class FeatureFlagsCriteriaMother {
  static organizationSlugsToMatch(key: string, environment: EFeatureFlagEnvironments): Criteria {
    const filters = Filters.build();

    filters.add(KeyFilter.buildEqual(key));


    filters.add(RemovedAtFilter.buildActive());
    filters.add(EnvironmentFilter.buildEqual(environment));

    return Criteria.build(filters);
  }

  static userSlugsToMatch(key: string, environment: EFeatureFlagEnvironments, slugs: string[]): Criteria {
    const filters = Filters.build();

    filters.add(KeyFilter.buildEqual(key));

    if (slugs.length > 0) {
      filters.add(OrganizationSlugFilter.buildIn(slugs));
    }

    filters.add(RemovedAtFilter.buildActive());
    filters.add(EnvironmentFilter.buildEqual(environment));

    return Criteria.build(filters);
  }
}
