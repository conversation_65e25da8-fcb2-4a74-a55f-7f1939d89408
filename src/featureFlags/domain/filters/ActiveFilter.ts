import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import { FeatureFlagStatus } from '../value-objects/FeatureFlagStatus';

import type { FeatureFlagKeys } from '../entities/FeatureFlag';

class FilterField extends FilterFieldBase<FeatureFlagKeys> {}

export class ActiveFilter {
  private static readonly field: FeatureFlagKeys = 'status';

  static build(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();
    const filterValues: FilterValue[] = FeatureFlagStatus.getEnabledStatuses().map(_status => FilterValue.build(_status));

    return new Filter(field, operator, filterValues);
  }
}
