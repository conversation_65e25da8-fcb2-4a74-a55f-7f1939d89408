import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FeatureFlagKeys } from '../entities/FeatureFlag';

class FilterField extends FilterFieldBase<FeatureFlagKeys> {}

export class OrganizationSlugFilter {
  private static readonly field: FeatureFlagKeys = 'organizationSlugs';

  static buildIn(slugs: string[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();
    const filterValue = slugs.map((slug: string) => FilterValue.build(slug));

    return new Filter(field, operator, filterValue);
  }
}
