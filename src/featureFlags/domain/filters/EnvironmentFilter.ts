import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FeatureFlagKeys } from '../entities/FeatureFlag';

class FilterField extends FilterFieldBase<FeatureFlagKeys> {}

export class EnvironmentFilter {
  private static readonly field: FeatureFlagKeys = 'environment';

  static buildEqual(key: string): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(key);

    return new Filter(field, operator, filterValue);
  }
}
