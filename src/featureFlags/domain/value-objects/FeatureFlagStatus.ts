import { FvEnum } from '@discocil/fv-domain-library/domain';

export enum EFeatureFlagStatuses {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  FORALL = 'forall'
}

export class FeatureFlagStatus extends FvEnum<EFeatureFlagStatuses> {
  static readonly values: string[] = Object.values(EFeatureFlagStatuses);

  private constructor(value: EFeatureFlagStatuses) {
    super(value, Object.values(EFeatureFlagStatuses));
  }

  static build(value: string): FeatureFlagStatus {
    if (!this.values.includes(value)) {
      value = EFeatureFlagStatuses.ACTIVE;
    }

    return new this(value as EFeatureFlagStatuses);
  }

  static getEnabledStatuses(): EFeatureFlagStatuses[] {
    return [
      EFeatureFlagStatuses.ACTIVE,
      EFeatureFlagStatuses.FORALL,
    ];
  }

  isActive(): boolean {
    return this.equalTo(EFeatureFlagStatuses.ACTIVE) || this.equalTo(EFeatureFlagStatuses.FORALL);
  }

  isInactive(): boolean {
    return this.equalTo(EFeatureFlagStatuses.INACTIVE);
  }

  isForAll(): boolean {
    return this.equalTo(EFeatureFlagStatuses.FORALL);
  }
}
