import type { RuleTemplate } from '@/organizations/organizations/domain/entities/RuleTemplate';
import type { PaymentGGDD } from '@/tickets/tickets/domain/services/TicketPaymentGGDDCalculate';
import type { Money } from '@discocil/fv-domain-library/domain';

export class BookingPaymentGGDDCalculate {
  constructor(
    private readonly ruleTemplate: RuleTemplate,
    private readonly totalBookingAmountToPay: Money,
  ) { }

  execute(): PaymentGGDD {
    const ggddTemplate = this.ruleTemplate.getGGDDBookings();

    const percentageComission = this.totalBookingAmountToPay.percentage(ggddTemplate.percentage);
    const totalFee = percentageComission.add(ggddTemplate.fixed);
    const boundedComission = this.ruleTemplate.getBookingBoundedCommissionFee(totalFee);

    const discocilTotal = this.totalBookingAmountToPay.lt(boundedComission)
      ? this.totalBookingAmountToPay
      : boundedComission;

    return {
      discocilTotal,
      ggddTemplate,
    };
  }
}
