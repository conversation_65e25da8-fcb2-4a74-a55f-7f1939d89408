import {
  left,
  Maybe,
  PersonalDocument,
  PhoneNumber,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { BookingCriteriaMother } from '../../domain/filters/BookingCriteriaMother';

import type { IGetBookingTypeService } from '@/bookings/bookingTypes/domain/contracts/GetBookingTypeContract';
import type { IGetBookingZoneService } from '@/bookings/bookingZones/domain/contracts/GetBookingZoneContract';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type {
  ActivateBookingDto,
  ActivateBookingEither,
  IActivateBookingPaymentService,
  ICheckBookingIsActivable,
} from '../../domain/contracts/ActivateBookingContracts';
import type { BookingRepository } from '../../domain/contracts/BookingRepository';
import type { ActivateBooking } from '../../domain/entities/BookingEntity';

export class ActivateBookingUseCase implements UseCase<ActivateBookingDto, Promise<ActivateBookingEither>> {
  constructor(
    private readonly bookingRepository: BookingRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly getBookingTypeService: IGetBookingTypeService,
    private readonly getBookingZoneService: IGetBookingZoneService,
    private readonly eventRepository: EventRepository,
    private readonly checkBookingIsActivable: ICheckBookingIsActivable,
    private readonly activateBookingPaymentService: IActivateBookingPaymentService,
    private readonly paymentRepositoty: PaymentRepository,
  ) {}

  @contextualizeError()
  async execute(dto: ActivateBookingDto): Promise<ActivateBookingEither> {
    const { errorUrl, redirectUrl } = dto;

    const bookingCriteria = BookingCriteriaMother.idToMatch(UniqueEntityID.build(dto.bookingId));
    const bookingResult = await this.bookingRepository.find(bookingCriteria);

    if (bookingResult.isLeft()) {
      return left(bookingResult.value);
    }

    const booking = bookingResult.value;

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(UniqueEntityID.build(booking.organizationId));
    const organizationResult = await this.organizationRepository.find(organizationCriteria);

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    const organization = organizationResult.value;

    const clienteId = UniqueEntityID.build(dto.userId);

    const bookingTypesResult = await this.getBookingTypeService.execute(booking);

    if (bookingTypesResult.isLeft()) {
      return left(bookingTypesResult.value);
    }

    const bookingType = bookingTypesResult.value;

    const bookingZoneResult = await this.getBookingZoneService.execute(booking);

    if (bookingZoneResult.isLeft()) {
      return left(bookingZoneResult.value);
    }

    const bookingZone = bookingZoneResult.value;

    const checkBookingIsActivable = await this.checkBookingIsActivable.execute(booking, bookingZone);

    if (checkBookingIsActivable.isLeft()) {
      return left(checkBookingIsActivable.value);
    }

    let event = Maybe.none<EventEntity>();

    if (booking.eventId.isEmpty()) {
      const eventCriteria = EventCriteriaMother.idToMatch(UniqueEntityID.build(booking.eventId.get()));
      const eventsResult = await this.eventRepository.find(eventCriteria);

      if (eventsResult.isLeft()) {
        return left(eventsResult.value);
      }

      event = Maybe.some(eventsResult.value);
    }

    const paymentResult = await this.activateBookingPaymentService.execute({
      booking,
      bookingType,
      zone: bookingZone,
      organization,
      event,
    });

    if (paymentResult.isLeft()) {
      return left(paymentResult.value);
    }

    const payment = paymentResult.value;

    const customerPersonalDocumentResult = PersonalDocument.build(dto.personalDocumentType, dto.personalDocumentNumber);

    if (customerPersonalDocumentResult.isLeft()) {
      return left(customerPersonalDocumentResult.value);
    }

    const personalDocument = customerPersonalDocumentResult.value;

    const customerPhoneResult = PhoneNumber.build(dto.phone);

    if (customerPhoneResult.isLeft()) {
      return left(customerPhoneResult.value);
    }

    const customerPhone = customerPhoneResult.value;

    const activateData: ActivateBooking = {
      email: dto.email,
      linkId: dto.linkId,
      suscriptorId: dto.suscriptorId,
      referrerId: dto.referrerId,
      purchaseId: dto.purchaseId,
      idx: dto.idx,
      remarketing: dto.remarketing,
      address: dto.address,
      personalDocument,
      postalCode: dto.postalCode,
      country: dto.country,
      phone: customerPhone,
      clientId: clienteId.value,
    };

    booking.activate(activateData);
    booking.setPayment(payment);

    this.paymentRepositoty.save(payment);

    this.bookingRepository.save(booking);

    return right({
      booking, payment, redirectUrl, errorUrl,
    });
  }
}
