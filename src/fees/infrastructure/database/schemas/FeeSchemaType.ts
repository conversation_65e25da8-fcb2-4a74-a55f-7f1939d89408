import type {
  ECustomFeeApplyType,
  ECustomFeeState, ECustomFeeType, ECustomFeeTypeCalculation,
} from '@discocil/fv-domain-library/domain';

type FeeCalculation = {
  type: ECustomFeeTypeCalculation;
  value: number;
};

export type FeeSchemaType = {
  _id: string;
  organization_id: string;
  name: string;
  type: ECustomFeeType;
  state: ECustomFeeState;
  apply_to: ECustomFeeApplyType[];
  fees_to_apply: string[];
  description?: string;
  calculation: FeeCalculation;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};
