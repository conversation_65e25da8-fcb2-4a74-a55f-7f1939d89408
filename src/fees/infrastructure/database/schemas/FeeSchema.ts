import {
  ECustomFeeApplyType, ECustomFeeState, ECustomFeeType,
  ECustomFeeTypeCalculation,
} from '@discocil/fv-domain-library/domain';

export const feeSchema = {
  _id: {
    type: String,
    required: true,
  },
  organization_id: {
    type: String,
    required: true,
    index: true,
  },
  apply_to: {
    type: [String],
    enum: Object.values(ECustomFeeApplyType),
    default: [],
  },
  fees_to_apply: {
    type: [String],
    default: [],
  },
  name: {
    type: String,
    required: true,
  },
  description: { type: String },
  type: {
    type: String,
    enum: Object.values(ECustomFeeType),
    required: true,
  },
  calculation: {
    type: {
      type: String,
      enum: Object.values(ECustomFeeTypeCalculation),
      required: true,
    },
    value: {
      type: Number,
      required: true,
    },
  },
  state: {
    type: String,
    enum: Object.values(ECustomFeeState),
    required: true,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
