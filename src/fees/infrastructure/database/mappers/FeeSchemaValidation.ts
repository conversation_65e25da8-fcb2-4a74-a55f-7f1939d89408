
import {
  ECustomFeeApplyType, ECustomFeeState, ECustomFeeType, ECustomFeeTypeCalculation,
} from '@discocil/fv-domain-library/domain';

import type { FeeSchemaType } from '@/fees/infrastructure/database/schemas/FeeSchemaType';
import type { JSONSchemaType } from 'ajv';

export const feeValidationSchema: JSONSchemaType<FeeSchemaType> = {
  title: 'Fee Json Schema Validation',
  required: ['_id', 'organization_id', 'name', 'type', 'calculation', 'state'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    organization_id: { type: 'string' },
    apply_to: { type: 'array', items: { type: 'string', enum: Object.values(ECustomFeeApplyType) } },
    fees_to_apply: { type: 'array', items: { type: 'string' } },
    name: { type: 'string' },
    description: { type: 'string', nullable: true },
    type: { type: 'string', enum: Object.values(ECustomFeeType) },
    calculation: {
      type: 'object',
      required: ['type', 'value'],
      properties: {
        type: { type: 'string', enum: Object.values(ECustomFeeTypeCalculation) },
        value: { type: 'number' },
      },
    },
    state: { type: 'string', enum: Object.values(ECustomFeeState) },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
