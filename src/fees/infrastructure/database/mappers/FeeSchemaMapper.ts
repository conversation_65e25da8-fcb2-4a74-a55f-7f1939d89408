import type { Fee } from '@/fees/domain/entities/Fee';
import type { FeeSchemaType } from '../schemas/FeeSchemaType';

export class FeeSchemaMapper {
  static execute(fee: Fee): FeeSchemaType {
    return {
      _id: fee.id,
      organization_id: fee.organizationId,
      apply_to: fee.applyTo,
      fees_to_apply: fee.feesToApply,
      name: fee.name,
      description: fee.description.fold(() => undefined, item => item),
      type: fee.type,
      calculation: fee.calculation,
      state: fee.state,
      created_at: fee.createdAt,
      created_by: fee.createdBy,
      updated_at: fee.updatedAt,
      updated_by: fee.updatedBy,
      removed_at: fee.removedAt,
      removed_by: fee.removedBy,
    };
  }
}
