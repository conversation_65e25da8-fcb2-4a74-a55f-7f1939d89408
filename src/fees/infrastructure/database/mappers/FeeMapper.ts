import { Maybe } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { Fee } from '@/fees/domain/entities/Fee';

import { feeValidationSchema } from './FeeSchemaValidation';

import type { FeeEither } from '@/fees/domain/entities/Fee';
import type Ajv from 'ajv';
import type { FeeSchemaType } from '../schemas/FeeSchemaType';

export class FeeMapper {
  static execute(data: FeeSchemaType): FeeEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(feeValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: FeeMapper.name,
        data,
        target: validate.errors,
      });

      return FeeSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: FeeSchemaType): FeeEither {
    return Fee.build({
      id: data._id,
      organizationId: data.organization_id,
      applyTo: data.apply_to,
      feesToApply: data.fees_to_apply,
      name: data.name,
      description: Maybe.fromValue(data.description),
      type: data.type,
      calculation: data.calculation,
      state: data.state,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}

export class FeeSoftMapper extends FeeMapper {
  static execute(data: FeeSchemaType): FeeEither {
    return this.buildEntity({
      ...data,
      apply_to: data.apply_to ?? [],
      fees_to_apply: data.fees_to_apply ?? [],
      description: data.description ?? undefined,
    });
  }
}
