import { PerformanceMeasurementDecorator } from '@discocil/fv-domain-library/application';
import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { SearchFeesUseCase } from '@/fees/application/SearchFeesUseCase';
import { OrganizationDependencyIdentifier } from '@/organizations/organizations/domain/dependencyIdentifier/OrganizationDependencyIdentifier';

import { FeeDependencyIdentifier } from '../../domain/dependencyIdentifier/FeeDependencyIdentifier';
import { FeeMongoRepository } from '../database/repositories/FeeMongoRepository';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { Logger } from '@discocil/fv-domain-library/domain';
import type { DependencyContainer } from 'tsyringe';

export const FeeContainer = {
  register: (): void => {
    container.register(FeeDependencyIdentifier.FeeRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new FeeMongoRepository(dbConnection);
      }),
    });

    container.register(SearchFeesUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new SearchFeesUseCase(
          container.resolve(FeeDependencyIdentifier.FeeRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
        );

        return new PerformanceMeasurementDecorator(
          SearchFeesUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });
  },
};
