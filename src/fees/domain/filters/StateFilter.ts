import {
  Filter, FilterField as FilterFieldBase, FilterOperator, FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';
import { ECustomFeeState } from '@discocil/fv-domain-library/domain';

import type { FeeKeys } from '../entities/Fee';

class FilterField extends FilterFieldBase<FeeKeys> {}

export class StateFilter {
  private static readonly field: FeeKeys = 'state';

  static buildActive(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(ECustomFeeState.ACTIVE);

    return new Filter(field, operator, filterValue);
  }

  static buildInactive(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(ECustomFeeState.INACTIVE);

    return new Filter(field, operator, filterValue);
  }
}
