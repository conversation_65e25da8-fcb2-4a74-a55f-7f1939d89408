import {
  Filter, FilterField as FilterFieldBase, FilterOperator, FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FeeKey<PERSON> } from '../entities/Fee';
import type { ECustomFeeApplyType } from '@discocil/fv-domain-library/domain';

class FilterField extends FilterFieldBase<FeeKeys> {}

export class ApplyToFilter {
  private static readonly field: FeeKeys = 'applyTo';

  static buildContains(key: ECustomFeeApplyType): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(key);

    return new Filter(field, operator, filterValue);
  }
}
