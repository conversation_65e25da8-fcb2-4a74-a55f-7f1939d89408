import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { StateFilter } from './StateFilter';
import { ApplyToFilter } from './ApplyToFilter';
import { FeesToApplyFilter } from './FeesToApplyFilter';

import type { UniqueEntityID, ECustomFeeApplyType } from '@discocil/fv-domain-library/domain';

export class FeeCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static idsToMatch(ids: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildIn(ids));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static organizationToMatch(organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(StateFilter.buildActive());
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static serviceTypeToMatch(applyTo: ECustomFeeApplyType, organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(StateFilter.buildActive());
    filters.add(ApplyToFilter.buildContains(applyTo));
    filters.add(RemovedAtFilter.buildActive());


    return Criteria.build(filters);
  }

  static feesToApplyThatContains(feeIds: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(FeesToApplyFilter.buildIn(feeIds));
    filters.add(StateFilter.buildActive());
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }
}
