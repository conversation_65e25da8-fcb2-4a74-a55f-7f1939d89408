import {
  Filter, FilterField as FilterFieldBase, FilterOperator, FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FeeKey<PERSON> } from '../entities/Fee';
import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

class FilterField extends FilterFieldBase<FeeKeys> {}

export class FeesToApplyFilter {
  private static readonly field: FeeKeys = 'feesToApply';

  static buildIn(feeIds: UniqueEntityID[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();
    const filterValues: FilterValue[] = feeIds.map(feeId => FilterValue.build(feeId.value));

    return new Filter(field, operator, filterValues);
  }
}
