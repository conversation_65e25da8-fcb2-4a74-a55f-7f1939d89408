import {
  AggregateRoot,
  Collection,
  FvNumber,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  ECustomFeeApplyType,
  ECustomFeeState,
  ECustomFeeType,
  ECustomFeeTypeCalculation,
  Either,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type FeeCalculation = {
  readonly type: ECustomFeeTypeCalculation;
  readonly value: FvNumber;
};

type FeeCalculationPrimitives = {
  readonly type: ECustomFeeTypeCalculation;
  readonly value: number;
};

export type FeePrimitives = Primitives<Omit<Fee, 'calculation'>> & {
  calculation: FeeCalculationPrimitives;
};

export type Fees = Collection<Fee>;

export type FeeEither = Either<NotFoundError | MapperError, Fee>;
export type FeesEither = Either<NotFoundError | MapperError, Fees>;

export type FeeKeys = keyof Fee;

export class Fee extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    private readonly _applyTo: Collection<ECustomFeeApplyType>,
    private readonly _feesToApply: Collection<UniqueEntityID>,
    readonly name: string,
    readonly description: Maybe<string>,
    readonly type: ECustomFeeType,
    private readonly _calculation: FeeCalculation,
    readonly state: ECustomFeeState,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: FeePrimitives): FeeEither {
    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);

    const applyTo = Collection.build(primitives.applyTo);
    const feesToApply = Collection.build(primitives.feesToApply.map(item => UniqueEntityID.build(item)));
    const description = primitives.description.map(item => item);

    const calculation: FeeCalculation = {
      type: primitives.calculation.type,
      value: FvNumber.build(primitives.calculation.value),
    };

    const stamps = stampValueObjects(primitives);

    const entity = new Fee(
      id,
      organizationId,
      applyTo,
      feesToApply,
      primitives.name,
      description,
      primitives.type,
      calculation,
      primitives.state,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get applyTo(): ECustomFeeApplyType[] {
    return this._applyTo.toArray();
  }

  get feesToApply(): IdPrimitive[] {
    return this._feesToApply.toArray().map(item => item.toPrimitive());
  }

  get calculation(): FeeCalculationPrimitives {
    return {
      type: this._calculation.type,
      value: this._calculation.value.toPrimitive(),
    };
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  toPrimitives(): FeePrimitives {
    return {
      id: this.id,
      organizationId: this.organizationId,
      applyTo: this.applyTo,
      feesToApply: this.feesToApply,
      name: this.name,
      description: this.description,
      type: this.type,
      calculation: this.calculation,
      state: this.state,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
