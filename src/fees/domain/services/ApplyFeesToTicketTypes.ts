import {
  ECustomFeeApplyType,
  left,
  Money,
  right,
} from '@discocil/fv-domain-library/domain';

import { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { TicketTypes, TicketTypeEither } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { FeesResponse } from '@discocil/fv-customized-fees-customers-library';
import type {
  Either,
  IdPrimitive,
  InvalidArgumentError,
  MoneyError,
  UnexpectedError,
  NotFoundError,
} from '@discocil/fv-domain-library/domain';
import type { Fees } from '../entities/Fee';
import type { GetPriceWithFeesCalculator } from './GetPriceWithFeesCalculator';

type ApplyFeesToTicketTypesInput = {
  readonly fees: Fees;
  readonly ticketTypes: TicketTypes;
};

type ApplyFeesToTicketTypesOutput = Either<InvalidArgumentError | NotFoundError | MapperError | UnexpectedError | MoneyError, TicketTypes>;

export class ApplyFeesToTicketTypes {
  constructor(
    private readonly getPriceWithFeesCalculator: GetPriceWithFeesCalculator,
  ) {}

  execute(input: ApplyFeesToTicketTypesInput): ApplyFeesToTicketTypesOutput {
    const { fees, ticketTypes } = input;

    const ticketTypesWithFees = new Map<IdPrimitive, TicketType>();

    for (const ticketTypeOriginal of ticketTypes.values()) {
      const { currency, id: ticketTypeId } = ticketTypeOriginal;

      const calculator = this.getPriceWithFeesCalculator.execute(fees, currency, ECustomFeeApplyType.TICKETS);

      const ticketTypeWithFees = this.applyFeesToTicketTypeOptions(ticketTypeOriginal, calculator);

      if (ticketTypeWithFees.isLeft()) {
        return left(ticketTypeWithFees.value);
      }

      ticketTypesWithFees.set(ticketTypeId, ticketTypeWithFees.value);
    }

    return right(ticketTypesWithFees);
  }

  private applyFeesToTicketTypeOptions(
    ticketTypeOriginal: TicketType,
    calculator: (price: number) => FeesResponse,
  ): TicketTypeEither {
    const ticketTypeCopy = TicketType.build(ticketTypeOriginal.toPrimitives());

    if (ticketTypeCopy.isLeft()) {
      return left(ticketTypeCopy.value);
    }

    const ticketType = ticketTypeCopy.value;

    for (const option of ticketType.getOptions()) {
      const optionPriceWithFees = calculator(option.price).amounts.total;

      const optionPriceWithFeesMoneyResult = Money.build({
        amount: optionPriceWithFees,
        currency: ticketType.currency,
      });

      if (optionPriceWithFeesMoneyResult.isLeft()) {
        return left(optionPriceWithFeesMoneyResult.value);
      }

      option.setPrice(optionPriceWithFeesMoneyResult.value);
    }

    return right(ticketType);
  }
}
