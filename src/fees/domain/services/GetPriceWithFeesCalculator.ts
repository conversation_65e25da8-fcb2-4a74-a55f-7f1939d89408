import { FeeCalculate } from '@discocil/fv-customized-fees-customers-library';

import type {
  FeeRequest,
  FeesPrimitive,
  FeesResponse,
} from '@discocil/fv-customized-fees-customers-library';
import type { ECurrency, ECustomFeeApplyType } from '@discocil/fv-domain-library/domain';
import type { Fees } from '../entities/Fee';

export class GetPriceWithFeesCalculator {
  execute(fees: Fees, currency: ECurrency, service: ECustomFeeApplyType): (price: number) => FeesResponse {
    return (price: number) => {
      const feeCalculateProps = {
        currency,
        service,
        fees: this.mapFeesToFeePrimitive(fees),
        applyTo: [{
          quantity: 1,
          price,
        }],
      };

      return this.calculateFeesAndTaxes(feeCalculateProps);
    };
  }

  private mapFeesToFeePrimitive(fees: Fees): FeesPrimitive {
    return fees.toArray().map((fee) => {
      const feePrimitive = fee.toPrimitives();

      return {
        ...feePrimitive,
        calculate: fee.calculation,
        feeToApply: fee.feesToApply,
      };
    });
  }

  private calculateFeesAndTaxes(feeRequest: FeeRequest): FeesResponse {
    const feeCalculate = new FeeCalculate(feeRequest);

    return feeCalculate.execute();
  }
}
