import {
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { FeeCriteriaMother } from '../domain/filters/FeeCriteriaMother';

import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { FeeRepository } from '../domain/contracts/FeeRepository';
import type { SearchFeesRequestDto } from '../domain/contracts/SearchFeesContracts';
import type { FeesEither } from '../domain/entities/Fee';

export type ISearchFeesUseCase = UseCase<SearchFeesRequestDto, Promise<FeesEither>>;
export class SearchFeesUseCase implements ISearchFeesUseCase {
  constructor(
    private readonly feeRepository: FeeRepository,
    private readonly organizationRepository: OrganizationRepository,
  ) {}

  @contextualizeError()
  async execute(dto: SearchFeesRequestDto): Promise<FeesEither> {
    const { applyTo, organizationId } = dto;

    const organizationResult = await this.organizationRepository.find(OrganizationCriteriaMother.idToMatch(organizationId));

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    const feeCriteriaOriginal = FeeCriteriaMother.serviceTypeToMatch(applyTo, organizationId);

    const feeResultOriginal = await this.feeRepository.search(feeCriteriaOriginal);

    if (feeResultOriginal.isLeft()) {
      return left(feeResultOriginal.value);
    }

    const feesOriginal = feeResultOriginal.value;

    if (feesOriginal.isEmpty()) {
      return feeResultOriginal;
    }

    const feeIds: UniqueEntityID[] = feesOriginal.toArray().map(fee => UniqueEntityID.build(fee.id));

    const feeCriteriaDepth = FeeCriteriaMother.feesToApplyThatContains(feeIds);

    const feeResultDepth = await this.feeRepository.search(feeCriteriaDepth);

    if (feeResultDepth.isLeft()) {
      return left(feeResultDepth.value);
    }

    const combinedResults = feesOriginal.merge(feeResultDepth.value);

    return right(combinedResults);
  }
}
