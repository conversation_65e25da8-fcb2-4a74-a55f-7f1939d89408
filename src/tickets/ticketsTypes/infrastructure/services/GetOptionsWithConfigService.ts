import {
  Collection,
  Either,
  FvDate,
  FvNumber,
  IdPrimitive,
  Maybe,
  UnexpectedError,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import {
  OptionConfig, TicketTypeOption, TicketTypeOptions,
} from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';
import { GetOptionsWithConfigEither, IGetOptionsWithConfigService } from '@/tickets/ticketsTypes/domain/contracts/GetOptionsWithConfigContracts';

import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { TicketsSold } from '@/tickets/tickets/domain/contracts/TicketTypes';
import type { GetOptionsWithConfigRequest } from '@/tickets/ticketsTypes/domain/contracts/GetOptionsWithConfigContracts';

type BuildOptionConfigRequest = {
  readonly totalSold: number;
  readonly option: TicketTypeOption;
  readonly currentOption: Maybe<TicketTypeOption>;
  readonly startDateEvent: FvDate;
  readonly now: FvDate;
};

type GetOptionsWithConfigForOneTicketTypeRequest = {
  readonly ticketType: TicketType;
  readonly eventId: UniqueEntityID;
  readonly startDateEvent: FvDate;
  readonly now: FvDate;
};

type GetOptionsWithConfigForOneTicketTypeEither = Either<UnexpectedError, TicketTypeOptions>;

export class GetOptionsWithConfigService implements IGetOptionsWithConfigService {
  constructor(private readonly ticketRepository: TicketRepository) {}

  @contextualizeError()
  async execute(dto: GetOptionsWithConfigRequest): Promise<GetOptionsWithConfigEither> {
    const { ticketTypes, event } = dto;

    const eventId = UniqueEntityID.build(event.id);
    const startDateEvent = FvDate.create(event.startDate);
    const now = FvDate.create();

    const newOptionsByTicketTypeId = new Map<IdPrimitive, TicketTypeOptions>();

    for (const ticketType of ticketTypes.values()) {
      const optionsWithConfigResult = await this.executeForOneTicketType({
        ticketType,
        eventId,
        startDateEvent,
        now,
      });

      if (optionsWithConfigResult.isLeft()) {
        return left(optionsWithConfigResult.value);
      }

      newOptionsByTicketTypeId.set(ticketType.id, optionsWithConfigResult.value);
    }

    return right(newOptionsByTicketTypeId);
  }


  private async executeForOneTicketType(dto: GetOptionsWithConfigForOneTicketTypeRequest): Promise<GetOptionsWithConfigForOneTicketTypeEither> {
    const {
      ticketType, eventId, startDateEvent, now,
    } = dto;

    const ticketTypeId = UniqueEntityID.build(ticketType.id);

    const criteria = TicketCriteriaMother.numberOfTicketsSold(ticketTypeId, eventId);
    const ticketsSoldResult = await this.ticketRepository.numberOfTicketsSold(criteria);

    if (ticketsSoldResult.isLeft()) {
      return left(ticketsSoldResult.value);
    }

    const ticketsSold = ticketsSoldResult.value;
    const options = ticketType.getSortedOptions();

    let currentOption = Maybe.none<TicketTypeOption>();

    const newOptions = Collection.new<TicketTypeOption>('id');

    for (const option of options) {
      const totalSold = this.getOptionTotalSold(ticketsSold, option.id);

      const optionConfig = this.buildOptionConfig({
        totalSold, option, currentOption, startDateEvent, now,
      });

      if (currentOption.isEmpty() && optionConfig.isFuture) {
        optionConfig.isCurrent = true;
        currentOption = Maybe.some(option);
      }

      const newOption = TicketTypeOption.build(option, ticketType.currency);

      if (newOption.isLeft()) {
        return left(newOption.value);
      }

      newOptions.add(newOption.value.setConfig(optionConfig));
    }

    return right(newOptions);
  }


  private buildOptionConfig(dto: BuildOptionConfigRequest): OptionConfig {
    const {
      totalSold, option, currentOption, startDateEvent, now,
    } = dto;

    const availableAmount = option.calculateAvailableAmount(totalSold);

    const optionConfig: OptionConfig = {
      availableAmount,
      isFuture: true,
      isCurrent: false,
      totalSold,
    };

    if (currentOption.isDefined()) {
      return optionConfig;
    }

    const optionEndDate = startDateEvent.addEpoch(option.to);

    const isAvailableDate = now.isLessThanOrEqualTo(optionEndDate);

    optionConfig.isFuture = isAvailableDate && FvNumber.build(optionConfig.availableAmount).isPositive();

    return optionConfig;
  }

  private getOptionTotalSold(ticketsSold: TicketsSold[], optionId: string): number {
    const ticketsSoldOption = ticketsSold.find(({ id }) => id === optionId);

    return ticketsSoldOption?.totalSales ?? 0;
  }
}
