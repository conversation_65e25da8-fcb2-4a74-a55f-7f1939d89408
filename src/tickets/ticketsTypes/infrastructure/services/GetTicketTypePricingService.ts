import {
  left, right, Money,
} from '@discocil/fv-domain-library/domain';

import { ggddBusinessCommission } from '@/cross-cutting/domain/commissions/service';
import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { IGetOptionsService } from '@/tickets/tickets/domain/contracts/GetOptionsContracts';

import type {
  GetTicketTypePricingEither, GetTicketTypePricingRequest, IGetTicketTypePricingService, TicketTypePricingsMoney,
} from '@/tickets/ticketsTypes/domain/contracts/PricingTicketContract';

export class GetTicketTypePricingService implements IGetTicketTypePricingService {
  constructor(private readonly getOptionsService: IGetOptionsService) {}

  @contextualizeError()
  async execute(dto: GetTicketTypePricingRequest): Promise<GetTicketTypePricingEither> {
    const { ticketType } = dto;

    const optionsResult = await this.getOptionsService.execute(dto);

    if (optionsResult.isLeft()) {
      return left(optionsResult.value);
    }

    const options = optionsResult.value;

    const ids = new Set<string>();
    const prices: TicketTypePricingsMoney = new Map<string, Money>();
    const fees: TicketTypePricingsMoney = new Map<string, Money>();
    const warranties: TicketTypePricingsMoney = new Map<string, Money>();

    for (const option of options) {
      const price = Money.build({ amount: option.price, currency: ticketType.currency }).value as Money;

      const ggdd = ggddBusinessCommission(
        option.ggddType,
        option.ggddAmount,
        price,
        ticketType.currency,
      );

      const warranty = ticketType.getWarrantyCost(price);

      ids.add(option.id);
      prices.set(option.id, price);
      warranties.set(option.id, warranty);
      fees.set(option.id, ggdd);
    }

    const samePrice = new Set([...prices.keys()]).size === 1;

    return right({
      ids,
      prices,
      samePrice,
      options,
      fees,
      warranties,
    });
  }
}
