import {
  EMicrositeChannel,
  Fv<PERSON><PERSON><PERSON>,
  Maybe,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { isNotFoundError } from '@/cross-cutting/domain/helpers/guards';
import { intersect } from '@/cross-cutting/domain/helpers/Intersect';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { TicketLimitCriteriaMother } from '@/tickets/ticketLimits/domain/filters/TicketLimitCriteriaMother';
import { TicketOrganizationLimitCriteriaMother } from '@/tickets/ticketOrganizationLimit/domain/filters/TicketOrganizationLimitCriteriaMother';
import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';

import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  IGetTicketTypeLimitsService,
  TicketTypeLimitsEither,
  TicketTypeLimitsRequest,
} from '@/tickets/ticketsTypes/domain/contracts/GetTicketTypeLimitsContracts';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { TicketLimitRepository } from '@/tickets/ticketLimits/domain/contracts/TicketLimitRepository';
import type { TicketOrganizationLimitRepository } from '@/tickets/ticketOrganizationLimit/domain/contracts/TicketOrganizationLimitRepository';
import type { TicketOrganizationLimit } from '@/tickets/ticketOrganizationLimit/domain/entities/TicketOrganizationLimitEntity';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { User } from '@/user/domain/entities/User';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

type LimitsResponse = {
  readonly maximum: number;
  readonly ticketGroupLimit?: TicketOrganizationLimit | null;
  readonly organizationAssignedId: Maybe<UniqueEntityID>;
};

type LimitsEither = Either<MapperError | NotFoundError, LimitsResponse>;

type SalesEither = Either<MapperError | NotFoundError | InvalidArgumentError | InvalidFieldError | UnexpectedError | MoneyError, number>;

export class GetTicketTypeLimitsService implements IGetTicketTypeLimitsService {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly ticketLimitRepository: TicketLimitRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly ticketOrganizationLimitRepository: TicketOrganizationLimitRepository,
  ) {}

  @contextualizeError()
  async execute(dto: TicketTypeLimitsRequest): Promise<TicketTypeLimitsEither> {
    const limitsResult = await this.getLimits(dto);

    if (limitsResult.isLeft()) {
      return left(limitsResult.value);
    }

    const {
      maximum, ticketGroupLimit, organizationAssignedId,
    } = limitsResult.value;

    const salesResult = await this.getSales(dto, organizationAssignedId, ticketGroupLimit);

    if (salesResult.isLeft()) {
      return left(salesResult.value);
    }

    const totalSales = salesResult.value;

    const maximumValue = maximum ?? 0;
    const salesAvailable = maximumValue - totalSales;

    return right({
      maximum: maximumValue,
      totalSales,
      available: FvNumber.max(salesAvailable, 0).toPrimitive(),
    });
  }

  private async getLimits(dto: TicketTypeLimitsRequest): Promise<LimitsEither> {
    const { ticketType, channel: { type, channel: micrositeChannel } } = dto;

    if (type === EMicrositeChannel.REFERRER) {
      return this.micrositeChannelReferrer(ticketType, micrositeChannel as User);
    }

    return this.micrositeChannelOrganization(ticketType, micrositeChannel as Organization);
  }

  private async getSales(
    dto: TicketTypeLimitsRequest,
    organizationAssignedId: Maybe<UniqueEntityID>,
    ticketGroupLimit?: TicketOrganizationLimit | null,
  ): Promise<SalesEither> {
    const { ticketType, channel } = dto;

    if (ticketGroupLimit) {
      const criteria = TicketCriteriaMother.groupSoldToMatch(
        UniqueEntityID.build(ticketType.eventId),
        UniqueEntityID.build(ticketType.id),
        UniqueEntityID.build(ticketType.organizationId),
        UniqueEntityID.build(ticketGroupLimit.organizationLimitedId),
      );

      const ticketResult = await this.ticketRepository.search(criteria);

      if (ticketResult.isLeft()) {
        return left(ticketResult.value);
      }

      const tickets = ticketResult.value;

      return right([...tickets.tickets.values()].reduce((acc, ticket) => acc + ticket.targeted, 0));
    }

    const totalTicketCriteria = this.getGuestListCriteria(channel, ticketType, organizationAssignedId);
    const totalsResult = await this.ticketRepository.getTotals(totalTicketCriteria);

    if (totalsResult.isLeft()) {
      return left(totalsResult.value);
    }

    return right(totalsResult.value.totalSales);
  }

  private async micrositeChannelReferrer(ticketType: TicketType, user: User): Promise<LimitsEither> {
    let maximum = 0;

    let criteria = TicketLimitCriteriaMother.specificLimitToMatch(
      UniqueEntityID.build(ticketType.eventId),
      ticketType.slug,
      UniqueEntityID.build(user.id),
    );

    let limitResult = await this.ticketLimitRepository.find(criteria);

    if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
      return left(limitResult.value);
    }

    if (limitResult.isRight()) {
      maximum = limitResult.value.maximum;
    }

    criteria = OrganizationCriteriaMother.hostGroupToMatch(ticketType.organizationId);
    const groupsResult = await this.organizationRepository.search(criteria);

    if (groupsResult.isLeft()) {
      return left(groupsResult.value);
    }

    const getGroupsIds = (groups: Organization[], userOrganizationsIds: string[]): UniqueEntityID[] => {
      const groupsIds = groups.map(item => item.id);

      return intersect(groupsIds, userOrganizationsIds).map(id => UniqueEntityID.build(id));
    };

    const groupsIds = getGroupsIds([...groupsResult.value.values()], user.organizations);

    if (limitResult.isLeft() && isNotFoundError(limitResult.value)) {
      criteria = TicketLimitCriteriaMother.personalLimitToMatch(
        UniqueEntityID.build(ticketType.eventId),
        ticketType.slug,
        groupsIds,
      );

      limitResult = await this.ticketLimitRepository.find(criteria);

      if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
        return left(limitResult.value);
      }

      if (limitResult.isRight()) {
        maximum = limitResult.value.maximum;
      }
    }

    const ticketOrganizationLimitCriteria = TicketOrganizationLimitCriteriaMother.groupLimitToMatch(
      UniqueEntityID.build(ticketType.id),
      UniqueEntityID.build(ticketType.eventId),
      groupsIds,
      UniqueEntityID.build(ticketType.organizationId),
    );

    const ticketGroupLimitResult = await this.ticketOrganizationLimitRepository.find(ticketOrganizationLimitCriteria);
    let ticketGroupLimit: TicketOrganizationLimit | null = null;

    if (ticketGroupLimitResult.isLeft() && !isNotFoundError(ticketGroupLimitResult.value)) {
      return left(ticketGroupLimitResult.value);
    }

    if (ticketGroupLimitResult.isRight()) {
      ticketGroupLimit = ticketGroupLimitResult.value;

      if (limitResult.isRight()) {
        maximum = limitResult.value.maximum;
      }

      maximum = FvNumber.min(maximum, ticketGroupLimit.limit).toPrimitive();
    }

    if (limitResult.isRight()) {
      return right({
        maximum,
        ticketGroupLimit,
        organizationAssignedId: Maybe.none(),
      });
    }

    criteria = OrganizationCriteriaMother.hostNotGroupToMatch(
      user.organizations.map(item => UniqueEntityID.build(item)),
      ticketType.organizationId,
    );

    const organizationsAssignedResult = await this.organizationRepository.search(criteria);

    if (organizationsAssignedResult.isLeft()) {
      return left(organizationsAssignedResult.value);
    }

    const organizationsAssigned = [...organizationsAssignedResult.value.values()];
    const organizationAssignedIds = organizationsAssigned.map(org => UniqueEntityID.build(org.id));

    criteria = TicketLimitCriteriaMother.personalLimitToMatch(
      UniqueEntityID.build(ticketType.eventId),
      ticketType.slug,
      organizationAssignedIds,
    );

    limitResult = await this.ticketLimitRepository.find(criteria);

    if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
      return left(limitResult.value);
    }

    let organizationAssignedId = Maybe.none<UniqueEntityID>();

    if (limitResult.isRight()) {
      maximum = limitResult.value.maximum;

      const limit = limitResult.value;

      organizationAssignedId = limit.organizationId.map(item => UniqueEntityID.build(item));
    }

    return right({
      maximum,
      ticketGroupLimit,
      organizationAssignedId,
    });
  }

  private async micrositeChannelOrganization(ticketType: TicketType, organization: Organization): Promise<LimitsEither> {
    if (!organization.id || organization.equalTo(ticketType.organizationId)) {
      return right({
        maximum: ticketType.clientLimit,
        organizationAssignedId: Maybe.none(),
      });
    }

    const criteria = TicketLimitCriteriaMother.organizationAssignedLimitToMatch(
      UniqueEntityID.build(ticketType.eventId),
      ticketType.slug,
      UniqueEntityID.build(organization.id),
    );

    const limitResult = await this.ticketLimitRepository.find(criteria);

    if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
      return left(limitResult.value);
    }

    const maximum = limitResult.isRight() ? limitResult.value.maximum : 0;

    return right({
      maximum,
      organizationAssignedId: Maybe.none(),
    });
  }

  private getGuestListCriteria(channel: MicrositeChannel, ticketType: TicketType, organizationAssignedId: Maybe<UniqueEntityID>): Criteria {
    const { type, channel: micrositeChannel } = channel;

    if (type === EMicrositeChannel.REFERRER) {
      const referrer = micrositeChannel as User;

      if (organizationAssignedId.isDefined()) {
        return TicketCriteriaMother.referrerOrganizationAssignedToMatch(
          organizationAssignedId.get(),
          UniqueEntityID.build(ticketType.eventId),
          UniqueEntityID.build(ticketType.id),
        );
      }

      return TicketCriteriaMother.organizationAssignedAndReferrerToMatch(
        UniqueEntityID.build(ticketType.organizationId),
        UniqueEntityID.build(referrer.id),
        UniqueEntityID.build(ticketType.eventId),
        UniqueEntityID.build(ticketType.id),
      );
    }

    const organization = micrositeChannel as Organization;

    return TicketCriteriaMother.organizationAssignedToMatch(
      UniqueEntityID.build(organization.id),
      UniqueEntityID.build(ticketType.eventId),
      UniqueEntityID.build(ticketType.id),
    );
  }
}
