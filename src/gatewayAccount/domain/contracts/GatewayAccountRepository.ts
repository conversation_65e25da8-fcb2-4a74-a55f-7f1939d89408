import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type {
  GatewayAccount,
  GatewayAccountEither,
  OrganizationGatewayPaymentEither,
} from '../entities/GatewayAccount';

export interface GatewayAccountRepository {
  find: (criteria: Criteria) => Promise<GatewayAccountEither>;
  save: (gatewayAccount: GatewayAccount) => Promise<void>;
}

export interface IGatewayAccountCacheRepository {
  find: (organizationId: UniqueEntityID) => Promise<OrganizationGatewayPaymentEither>;
  save: (gatewayAccount: GatewayAccount, organization: Organization) => Promise<void>;
}
