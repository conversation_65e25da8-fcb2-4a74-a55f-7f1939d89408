import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class GatewayAccountCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }
}
