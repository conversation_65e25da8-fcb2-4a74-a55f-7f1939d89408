import {
  AggregateRoot,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  Maybe,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';
import type { PaymentGatewayBadConfigurationError } from '../errors/PaymentGatewayBadConfigurationError';

export type GatewayAccountPrimitives = Primitives<GatewayAccount>;

export type OrganizationGatewayPayment = {
  readonly isFourvenuesAccount: boolean;
  readonly organizationId: UniqueEntityID;
  readonly gatewayAccountId: Maybe<UniqueEntityID>;
};

export type GatewayAccountEither = Either<NotFoundError | MapperError, GatewayAccount>;
export type OrganizationGatewayPaymentEither = Either<NotFoundError | MapperError | PaymentGatewayBadConfigurationError, OrganizationGatewayPayment>;

export class GatewayAccount extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    readonly name: string,
    readonly provider: string,
    readonly token: string,
    readonly secret: string,
    readonly description: Maybe<string>,
    readonly isFourvenuesAccount: boolean,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: GatewayAccountPrimitives): GatewayAccountEither {
    const id = UniqueEntityID.build(primitives.id);

    const description = primitives.description.map(item => item);

    const stamps = stampValueObjects(primitives);

    const entity = new GatewayAccount(
      id,
      primitives.name,
      primitives.provider,
      primitives.token,
      primitives.secret,
      description,
      primitives.isFourvenuesAccount,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  toPrimitives(): GatewayAccountPrimitives {
    return {
      id: this.id,
      name: this.name,
      provider: this.provider,
      token: this.token,
      secret: this.secret,
      description: this.description,
      isFourvenuesAccount: this.isFourvenuesAccount,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
