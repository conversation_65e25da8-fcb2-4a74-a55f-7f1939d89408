import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class PaymentGatewayBadConfigurationError extends FvError {
  static readonly defaultCause = EErrorKeys.PAYMENT_GATEWAY_BAD_CONFIGURATION;

  static build(request: ErrorMethodRequest): PaymentGatewayBadConfigurationError {
    const exceptionMessage = 'Misconfigured payment gateway';

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
