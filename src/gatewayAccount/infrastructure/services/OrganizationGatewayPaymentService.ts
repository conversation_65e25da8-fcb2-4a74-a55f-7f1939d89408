import {
  EPaymentsProvider,
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { PaymentGatewayBadConfigurationError } from '@/gatewayAccount/domain/errors/PaymentGatewayBadConfigurationError';
import { GatewayAccountCriteriaMother } from '@/gatewayAccount/domain/filters/GatewayAccountCriteriaMother';

import { OrganizationGatewayPaymentMapper } from '../database/mappers/OrganizationGatewayPaymentMapper';

import type { IOrganizationGatewayPaymentService } from '@/gatewayAccount/domain/contracts/GatewayAccountContracts';
import type { GatewayAccountRepository, IGatewayAccountCacheRepository } from '@/gatewayAccount/domain/contracts/GatewayAccountRepository';
import type { OrganizationGatewayPaymentEither } from '@/gatewayAccount/domain/entities/GatewayAccount';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';

export class OrganizationGatewayPaymentService implements IOrganizationGatewayPaymentService {
  constructor(
    private readonly gatewayAccountRepository: GatewayAccountRepository,
    private readonly gatewayAccountCacheRepository: IGatewayAccountCacheRepository,
  ) {}

  @contextualizeError()
  async execute(organization: Organization): Promise<OrganizationGatewayPaymentEither> {
    if (organization.hasPaymentProvider(EPaymentsProvider.ADYEN)) {
      const response = OrganizationGatewayPaymentMapper.fromAdyenProvider(organization);

      return right(response);
    }

    const organizationGatewayPaymentResult = await this.gatewayAccountCacheRepository.find(UniqueEntityID.build(organization.id));

    if (organizationGatewayPaymentResult.isRight()) {
      return right(organizationGatewayPaymentResult.value);
    }

    if (organization.gatewayAccountId.isEmpty()) {
      return left(PaymentGatewayBadConfigurationError.build({ context: this.constructor.name }).notAutoContextualizable());
    }

    const criteria = GatewayAccountCriteriaMother.idToMatch(UniqueEntityID.build(organization.gatewayAccountId.get()));
    const gatewayAccountResult = await this.gatewayAccountRepository.find(criteria);

    if (gatewayAccountResult.isLeft()) {
      return left(gatewayAccountResult.value);
    }

    const gatewayAccount = gatewayAccountResult.value;
    const response = OrganizationGatewayPaymentMapper.fromDatabase(organization, gatewayAccount);

    await this.gatewayAccountCacheRepository.save(gatewayAccount, organization);

    return right(response);
  }
}
