import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { GatewayAccountDependencyIdentifier } from '../../domain/dependencyIdentifier/GatewayAccountDependencyIdentifier';
import { GatewayAccountCacheRepository } from '../database/repositories/GatewayAccountCacheRepository';
import { GatewayAccountMongoRepository } from '../database/repositories/GatewayAccountMongoRepository';
import { OrganizationGatewayPaymentService } from '../services/OrganizationGatewayPaymentService';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { DependencyContainer } from 'tsyringe';

export const GatewayAccountContainer = {
  register: (): void => {
    container.register(GatewayAccountDependencyIdentifier.GatewayAccountDataBaseRepository, {
      useFactory: instanceCachingFactory(() => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new GatewayAccountMongoRepository(dbConnection);
      }),
    });

    container.register(GatewayAccountDependencyIdentifier.GatewayAccountCacheRepository, {
      useFactory: instanceCachingFactory((container: DependencyContainer) => {
        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheRepository = new GatewayAccountCacheRepository(cacheHander);

        return cacheRepository;
      }),
    });

    container.register(GatewayAccountDependencyIdentifier.OrganizationGatewayPaymentService, {
      useFactory: instanceCachingFactory(() => {
        return new OrganizationGatewayPaymentService(
          container.resolve(GatewayAccountDependencyIdentifier.GatewayAccountDataBaseRepository),
          container.resolve(GatewayAccountDependencyIdentifier.GatewayAccountCacheRepository),
        );
      }),
    });
  },
};
