import {
  FvDate,
  left,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { GatewayAccount } from '@/gatewayAccount/domain/entities/GatewayAccount';

import { OrganizationGatewayPaymentMapper } from '../mappers/OrganizationGatewayPaymentMapper';
import { OrganizationGatewayPaymentSchemaMapper } from '../mappers/OrganizationGatewayPaymentSchemaMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { IGatewayAccountCacheRepository } from '@/gatewayAccount/domain/contracts/GatewayAccountRepository';
import type { OrganizationGatewayPaymentEither } from '@/gatewayAccount/domain/entities/GatewayAccount';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { OrganizationGatewayPaymentSchemaType } from '../schemas/GatewayAccountSchemaType';

export class GatewayAccountCacheRepository implements IGatewayAccountCacheRepository {
  constructor(private readonly cacheHandler: ICacheRepository) {}

  private getKey(organizationId: UniqueEntityID): string {
    return `shared:payments:organization_gateway_info:${organizationId.value}`;
  }

  async find(organizationId: UniqueEntityID): Promise<OrganizationGatewayPaymentEither> {
    const cacheKey = this.getKey(organizationId);
    const fromCache = await this.cacheHandler.get(cacheKey);

    if (!fromCache || fromCache === 'null') {
      return left(NotFoundError.build({ context: this.constructor.name, target: GatewayAccount.name }));
    }

    const parsedData: OrganizationGatewayPaymentSchemaType = JSON.parse(fromCache);
    const response = OrganizationGatewayPaymentMapper.fromCache(parsedData);

    return right(response);
  }

  async save(gatewayAccount: GatewayAccount, organization: Organization): Promise<void> {
    const cacheKey = this.getKey(UniqueEntityID.build(organization.id));
    const dataToCache = OrganizationGatewayPaymentSchemaMapper.execute(gatewayAccount, organization);

    await this.cacheHandler.set(cacheKey, dataToCache, FvDate.IN_SECONDS.ONE_HOUR);
  }
}
