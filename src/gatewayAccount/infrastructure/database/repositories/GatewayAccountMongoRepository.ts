import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { GatewayAccount } from '@/gatewayAccount/domain/entities/GatewayAccount';

import { GatewayAccountMapper } from '../mappers/GatewayAccountMapper';
import { GatewayAccountSchemaMapper } from '../mappers/GatewayAccountSchemaMapper';
import { gatewayAccountSchema } from '../schemas/GatewayAccountSchema';

import type { GatewayAccountRepository } from '@/gatewayAccount/domain/contracts/GatewayAccountRepository';
import type { GatewayAccountEither } from '@/gatewayAccount/domain/entities/GatewayAccount';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { GatewayAccountSchemaType } from '../schemas/GatewayAccountSchemaType';

export class GatewayAccountMongoRepository extends MongoRepository implements GatewayAccountRepository {
  protected getSchema(): Schema {
    return new Schema(gatewayAccountSchema);
  }

  protected getDBName(): string {
    return EDBNames.INTRANET;
  }

  protected getModel(): string {
    return 'gateway_accounts';
  }

  async find(criteria: Criteria): Promise<GatewayAccountEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<GatewayAccountSchemaType>();

    return queryResponse
      ? GatewayAccountMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: GatewayAccount.name }));
  }

  async save(gatewayAccount: GatewayAccount): Promise<void> {
    const toSave = GatewayAccountSchemaMapper.execute(gatewayAccount);

    const filter = { _id: gatewayAccount.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const modelQuery = await this.getConnection();

    await modelQuery.updateOne(filter, update, options);
  }
}
