import { FvDate } from '@discocil/fv-domain-library/domain';

export const gatewayAccountSchema = {
  _id: { type: String },
  name: {
    type: String,
    required: true,
  },
  provider: {
    type: String,
    required: true,
  },
  token: {
    type: String,
    required: true,
    index: true,
  },
  secret: {
    type: String,
    required: true,
  },
  description: { type: String },
  is_fourvenues_account: { type: Boolean },
  created_at: {
    type: Number,
    default: FvDate.create().toMilliseconds(),
    index: true,
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_at: {
    type: Number,
    default: FvDate.create().toMilliseconds(),
    index: true,
  },
  updated_by: {
    type: String,
    default: '',
  },
  removed_at: {
    type: Number,
    default: 0,
    index: true,
  },
  removed_by: {
    type: String,
    default: '',
    select: false,
  },
};
