export type GatewayAccountSchemaType = {
  _id: string;
  name: string;
  provider: string;
  token: string;
  secret: string;
  description?: string;
  is_fourvenues_account: boolean;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};

export type OrganizationGatewayPaymentSchemaType = {
  is_fourvenues_account: boolean;
  organization_id: string;
  gateway_account_id: string | null;
};
