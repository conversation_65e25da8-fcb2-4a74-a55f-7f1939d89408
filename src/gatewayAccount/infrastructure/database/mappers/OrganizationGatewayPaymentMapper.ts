import { Maybe, UniqueEntityID } from '@discocil/fv-domain-library/domain';

import type { GatewayAccount, OrganizationGatewayPayment } from '@/gatewayAccount/domain/entities/GatewayAccount';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { OrganizationGatewayPaymentSchemaType } from '../schemas/GatewayAccountSchemaType';

export class OrganizationGatewayPaymentMapper {
  static fromCache(data: OrganizationGatewayPaymentSchemaType): OrganizationGatewayPayment {
    return {
      isFourvenuesAccount: data.is_fourvenues_account,
      organizationId: UniqueEntityID.build(data.organization_id),
      gatewayAccountId: data.gateway_account_id ? Maybe.some(UniqueEntityID.build(data.gateway_account_id)) : Maybe.none<UniqueEntityID>(),
    };
  }

  static fromAdyenProvider(organization: Organization): OrganizationGatewayPayment {
    return {
      isFourvenuesAccount: !organization.hasExternalMerchant(),
      organizationId: UniqueEntityID.build(organization.id),
      gatewayAccountId: Maybe.none(),
    };
  }

  static fromDatabase(organization: Organization, gatewayAccount: GatewayAccount): OrganizationGatewayPayment {
    return {
      isFourvenuesAccount: gatewayAccount.isFourvenuesAccount,
      organizationId: UniqueEntityID.build(organization.id),
      gatewayAccountId: organization.gatewayAccountId.map(item => UniqueEntityID.build(item)),
    };
  }
}
