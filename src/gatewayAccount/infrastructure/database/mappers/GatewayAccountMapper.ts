import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { GatewayAccount } from '@/gatewayAccount/domain/entities/GatewayAccount';

import { gatewayAccountValidationSchema } from './GatewayAccountSchemaValidation';

import type { GatewayAccountEither } from '@/gatewayAccount/domain/entities/GatewayAccount';
import type Ajv from 'ajv';
import type { GatewayAccountSchemaType } from '../schemas/GatewayAccountSchemaType';

export class GatewayAccountMapper {
  static execute(data: GatewayAccountSchemaType): GatewayAccountEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(gatewayAccountValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: GatewayAccountMapper.name,
        data,
        target: validate.errors,
      });

      return GatewayAccountSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: GatewayAccountSchemaType): GatewayAccountEither {
    return GatewayAccount.build({
      id: data._id,
      name: data.name,
      provider: data.provider,
      token: data.token,
      secret: data.secret,
      description: Maybe.fromValue(data.description),
      isFourvenuesAccount: data.is_fourvenues_account ?? false,
      createdAt: data.created_at ?? FvDate.create().toMilliseconds(),
      createdBy: data.created_by,
      updatedAt: data.updated_at ?? FvDate.create().toMilliseconds(),
      updatedBy: data.updated_by,
      removedAt: data.removed_at ?? FvDate.create().toMilliseconds(),
      removedBy: data.removed_by,
    });
  }
}

export class GatewayAccountSoftMapper extends GatewayAccountMapper {
  static execute(data: GatewayAccountSchemaType): GatewayAccountEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      name: data.name ?? null,
      provider: data.provider ?? null,
      token: data.token ?? null,
      secret: data.secret ?? null,
    });
  }
}
