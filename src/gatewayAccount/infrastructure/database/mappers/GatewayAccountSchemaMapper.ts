import type { GatewayAccount } from '@/gatewayAccount/domain/entities/GatewayAccount';
import type { GatewayAccountSchemaType } from '../schemas/GatewayAccountSchemaType';

export class GatewayAccountSchemaMapper {
  static execute(gatewayAccount: GatewayAccount): GatewayAccountSchemaType {
    return {
      _id: gatewayAccount.id,
      name: gatewayAccount.name,
      provider: gatewayAccount.provider,
      token: gatewayAccount.token,
      secret: gatewayAccount.secret,
      description: gatewayAccount.description.fold(() => undefined, item => item),
      is_fourvenues_account: gatewayAccount.isFourvenuesAccount,
      created_at: gatewayAccount.createdAt,
      created_by: gatewayAccount.createdBy,
      updated_at: gatewayAccount.updatedAt,
      updated_by: gatewayAccount.updatedBy,
      removed_at: gatewayAccount.removedAt,
      removed_by: gatewayAccount.removedBy,
    };
  }
}
