import type { GatewayAccountSchemaType } from '../schemas/GatewayAccountSchemaType';
import type { JSONSchemaType } from 'ajv';

export const gatewayAccountValidationSchema: JSONSchemaType<GatewayAccountSchemaType> = {
  title: 'GuestListType Json Schema Validation',
  required: ['_id', 'name', 'provider', 'token', 'secret'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    name: { type: 'string' },
    provider: { type: 'string' },
    token: { type: 'string' },
    secret: { type: 'string' },
    description: { type: 'string', nullable: true },
    is_fourvenues_account: { type: 'boolean' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
