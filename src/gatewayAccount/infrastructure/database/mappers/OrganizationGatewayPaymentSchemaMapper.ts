import type { GatewayAccount } from '@/gatewayAccount/domain/entities/GatewayAccount';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { OrganizationGatewayPaymentSchemaType } from '../schemas/GatewayAccountSchemaType';

export class OrganizationGatewayPaymentSchemaMapper {
  static execute(gatewayAccount: GatewayAccount, organization: Organization): OrganizationGatewayPaymentSchemaType {
    return {
      is_fourvenues_account: gatewayAccount.isFourvenuesAccount,
      organization_id: organization.id,
      gateway_account_id: organization.gatewayAccountId.fold(() => null, item => item),
    };
  }
}
