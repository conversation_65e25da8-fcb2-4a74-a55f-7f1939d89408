import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { FeeCalculationResultDependencyIdentifier } from '../../domain/dependencyIdentifier/FeeCalculationResultDependencyIdentifier';
import { FeeCalculationResultMongoRepository } from '../database/repositories/FeeCalculationResultMongoRepository';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const FeeCalculationResultContainer = {
  register: (): void => {
    container.register(FeeCalculationResultDependencyIdentifier.FeeCalculationResultRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new FeeCalculationResultMongoRepository(dbConnection);
      }),
    });
  },
};
