import {
  DomainEvent,
  DomainEventClass,
  DomainEventSubscriber,
} from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { FeeCalculationResultCreatedDomainEvent } from '@/feeCalculationResult/domain/events/FeeCalculationResultCreatedDomainEvent';
import { FeeCalculationResult, FeeCalculationResultPrimitives } from '@/feeCalculationResult/domain/entities/FeeCalculationResult';

import type { FeeCalculationResultRepository } from '@/feeCalculationResult/domain/contracts/FeeCalculationResultRepository';
import type { Logger } from '@discocil/fv-domain-library/domain';

@singleton()
export class FeeCalculationResultListener implements DomainEventSubscriber<FeeCalculationResultCreatedDomainEvent> {
  constructor(
    private readonly feeCalculationResultRepository: FeeCalculationResultRepository,
    private readonly logger: Logger,
  ) {}

  subscribedTo(): DomainEventClass[] {
    return [FeeCalculationResultCreatedDomainEvent];
  }

  async on(domainEvent: DomainEvent): Promise<void> {
    const { attributes } = domainEvent;

    const feeCalculationResultPrimitives = attributes as FeeCalculationResultPrimitives;
    const feeCalculationResultResult = FeeCalculationResult.build(feeCalculationResultPrimitives);

    if (feeCalculationResultResult.isLeft()) {
      return;
    }

    const feeCalculationResult = feeCalculationResultResult.value;

    await this.feeCalculationResultRepository.save(feeCalculationResult);

    this.logger.info('FeeCalculationResult Listener');
  }
}
