import type { FeeCalculationResult } from '@/feeCalculationResult/domain/entities/FeeCalculationResult';
import type { FeeCalculationResultSchemaType } from '../schemas/FeeCalculationResultSchemaType';

export class FeeCalculationResultSchemaMapper {
  static execute(feeCalculationResult: FeeCalculationResult): FeeCalculationResultSchemaType {
    return {
      _id: feeCalculationResult.id,
      payment_id: feeCalculationResult.paymentId.fold(() => undefined, item => item),
      order_id: feeCalculationResult.orderId.fold(() => undefined, item => item),
      fees: {
        tickets: feeCalculationResult.fees.tickets.map(ticket => ({
          ticket_type_id: ticket.ticketTypeId,
          option_id: ticket.optionId,
          quantity: ticket.quantity,
          unit_price: ticket.unitPrice,
          unit_fee_amount: ticket.unitFeeAmount,
          unit_tax_amount: ticket.unitTaxAmount,
          fee_type: ticket.feeType,
          applied_fees: ticket.appliedFees,
        })),
      },
      created_at: feeCalculationResult.createdAt,
      created_by: feeCalculationResult.createdBy,
      updated_at: feeCalculationResult.updatedAt,
      updated_by: feeCalculationResult.updatedBy,
      removed_at: feeCalculationResult.removedAt,
      removed_by: feeCalculationResult.removedBy,
    };
  }
}
