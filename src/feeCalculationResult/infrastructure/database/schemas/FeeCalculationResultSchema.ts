import {
  ECurrency, ECustomFeeType, ECustomFeeTypeCalculation,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { FeeCalculationResultFeeType } from '@/feeCalculationResult/domain/entities/FeeCalculationResult';

const CalculationSchema = new Schema({
  type: {
    type: String,
    enum: Object.values(ECustomFeeTypeCalculation),
    required: true,
  },
  value: {
    type: Number,
    required: true,
  },
}, { _id: false });

const AmountsSchema = new Schema({
  subTotal: {
    type: Number,
    required: true,
  },
  fees: {
    type: Number,
    required: true,
  },
  taxes: {
    type: Number,
    required: true,
  },
  total: {
    type: Number,
    required: true,
  },
}, { _id: false });

const TreeOfFeesToApplySchema = new Schema({
  id: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: Object.values(ECustomFeeType),
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  calculate: { type: CalculationSchema },
  subFees: { default: [] },
}, { _id: false });

TreeOfFeesToApplySchema.add({
  subFees: {
    type: [TreeOfFeesToApplySchema],
    default: [],
  },
});

const FeesResponseSchema = new Schema({
  currency: {
    type: String,
    default: ECurrency.EUR,
    enum: Object.values(ECurrency),
  },
  amounts: {
    type: AmountsSchema,
    required: true,
  },
  fees: {
    type: [TreeOfFeesToApplySchema],
    default: [],
  },
}, { _id: false });

const TicketFeesSchema = new Schema({
  ticket_type_id: { type: String },
  option_id: { type: String },
  quantity: {
    type: Number,
    required: true,
  },
  unit_price: {
    type: Number,
    required: true,
  },
  unit_fee_amount: {
    type: Number,
    required: true,
    default: 0,
  },
  unit_tax_amount: {
    type: Number,
    required: true,
    default: 0,
  },
  fee_type: {
    type: String,
    enum: Object.values(FeeCalculationResultFeeType),
  },
  applied_fees: {
    type: FeesResponseSchema,
    required: true,
  },
}, { _id: false });

const FeesSchema = new Schema({
  tickets: {
    type: [TicketFeesSchema],
    default: [],
  },
}, { _id: false });

export const feeCalculationResultSchema = {
  _id: {
    type: String,
    required: true,
  },
  payment_id: {
    type: String,
    index: true,
  },
  order_id: {
    type: String,
    index: true,
  },
  fees: {
    type: FeesSchema,
    required: true,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
