import type { FeesResponse } from '@discocil/fv-customized-fees-customers-library';
import type { FeeCalculationResultFeeType } from '@/feeCalculationResult/domain/entities/FeeCalculationResult';

export type FeeCalculationResultSchemaType = {
  _id: string;
  payment_id?: string;
  order_id?: string;
  fees: {
    tickets: {
      ticket_type_id: string;
      option_id: string;
      quantity: number;
      unit_price: number;
      unit_fee_amount: number;
      unit_tax_amount: number;
      fee_type: FeeCalculationResultFeeType;
      applied_fees: FeesResponse;
    }[];
  };
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};
