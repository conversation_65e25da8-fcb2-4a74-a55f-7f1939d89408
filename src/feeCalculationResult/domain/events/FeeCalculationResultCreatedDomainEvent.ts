import { DomainEvent } from '@discocil/fv-domain-library/domain';

import type { DomainEventRequest } from '@discocil/fv-domain-library/domain';

export class FeeCalculationResultCreatedDomainEvent extends DomainEvent {
  static readonly EVENT_NAME: string = 'cli.feeCalculationResult.created';

  static build(params: DomainEventRequest): FeeCalculationResultCreatedDomainEvent {
    return new this({
      ...params,
      type: this.EVENT_NAME,
    });
  }
}
