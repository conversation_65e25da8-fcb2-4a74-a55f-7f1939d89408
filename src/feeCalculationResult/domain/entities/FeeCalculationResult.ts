import {
  AggregateRoot,
  Collection,
  ECustomFeeType,
  FvNumber,
  Maybe, Money,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { defaultStamps, stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { FeesResponse } from '@discocil/fv-customized-fees-customers-library';
import type {
  CreatedAt, CreatedBy,
  Either,
  IdPrimitive,
  InvalidArgumentError, MoneyError, NotFoundError,
  Primitives,
  RemovedAt, RemovedBy, UpdatedAt, UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type FeeCalculationResultPrimitives = Primitives<Omit<FeeCalculationResult, 'fees'>> & {
  fees: FeeCalculationResultFeesPrimitives;
};

export type FeeCalculationResultEither = Either<InvalidArgumentError | NotFoundError | MoneyError, FeeCalculationResult>;

export type FeeCalculationResultKeys = keyof FeeCalculationResult;

export enum FeeCalculationResultFeeType {
  FEE = ECustomFeeType.FEE,
  TAX = ECustomFeeType.TAX,
  ALL = 'all'
}

export type TicketFeeCalculationResult = {
  ticketTypeId: UniqueEntityID;
  optionId: string;
  quantity: FvNumber;
  unitPrice: Money;
  unitFeeAmount: Money;
  unitTaxAmount: Money;
  feeType: FeeCalculationResultFeeType;
  appliedFees: FeesResponse;
};

type TicketFeeCalculationResultPrimitives = {
  ticketTypeId: IdPrimitive;
  optionId: string;
  quantity: number;
  unitPrice: number;
  unitFeeAmount: number;
  unitTaxAmount: number;
  feeType: FeeCalculationResultFeeType;
  appliedFees: FeesResponse;
};

type FeeCalculationResultFees = {
  tickets: Collection<TicketFeeCalculationResult>;
};

export type FeeCalculationResultFeesPrimitives = {
  tickets: TicketFeeCalculationResultPrimitives[];
};

export class FeeCalculationResult extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private _paymentId: Maybe<UniqueEntityID>,
    private _orderId: Maybe<UniqueEntityID>,
    private readonly _fees: FeeCalculationResultFees,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: FeeCalculationResultPrimitives): FeeCalculationResultEither {
    const id = UniqueEntityID.build(primitives.id);
    const paymentId = primitives.paymentId.map(item => UniqueEntityID.build(item));
    const orderId = primitives.orderId.map(item => UniqueEntityID.build(item));

    const fees = { tickets: Collection.new<TicketFeeCalculationResult>('optionId') };

    for (const ticketFeeCalculationResult of primitives.fees.tickets) {
      const currency = ticketFeeCalculationResult.appliedFees.currency;

      const unitPriceOrError = Money.build({ amount: ticketFeeCalculationResult.unitPrice, currency });

      if (unitPriceOrError.isLeft()) {
        return left(unitPriceOrError.value);
      }

      const unitFeeAmountOrError = Money.build({ amount: ticketFeeCalculationResult.unitFeeAmount, currency });

      if (unitFeeAmountOrError.isLeft()) {
        return left(unitFeeAmountOrError.value);
      }

      const unitTaxAmountOrError = Money.build({ amount: ticketFeeCalculationResult.unitTaxAmount, currency });

      if (unitTaxAmountOrError.isLeft()) {
        return left(unitTaxAmountOrError.value);
      }

      fees.tickets.add({
        ticketTypeId: UniqueEntityID.build(ticketFeeCalculationResult.ticketTypeId),
        optionId: ticketFeeCalculationResult.optionId,
        quantity: FvNumber.build(ticketFeeCalculationResult.quantity),
        unitPrice: unitPriceOrError.value,
        unitFeeAmount: unitFeeAmountOrError.value,
        unitTaxAmount: unitTaxAmountOrError.value,
        feeType: ticketFeeCalculationResult.feeType,
        appliedFees: ticketFeeCalculationResult.appliedFees,
      });
    }

    const stamps = stampValueObjects(primitives);

    const entity = new FeeCalculationResult(
      id,
      paymentId,
      orderId,
      fees,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  static create(): FeeCalculationResultEither {
    const id = UniqueEntityID.create().toPrimitive();
    const paymentId = Maybe.none<string>();
    const orderId = Maybe.none<string>();
    const fees = { tickets: [] as TicketFeeCalculationResultPrimitives[] };

    const entityResult = FeeCalculationResult.build({
      id,
      paymentId,
      orderId,
      fees,
      ...defaultStamps(),
    });

    return entityResult;
  }

  get paymentId(): Maybe<IdPrimitive> {
    return this._paymentId.fold(() => Maybe.none(), item => Maybe.some(item.value));
  }

  get orderId(): Maybe<IdPrimitive> {
    return this._orderId.fold(() => Maybe.none(), item => Maybe.some(item.value));
  }

  get fees(): FeeCalculationResultFeesPrimitives {
    return { tickets: this.getTicketFeeCalculationResult() };
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.value;
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.value;
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.value;
  }

  getTicketFeeCalculationResult(): TicketFeeCalculationResultPrimitives[] {
    return this._fees.tickets.toArray().map(item => ({
      ticketTypeId: item.ticketTypeId.value,
      optionId: item.optionId,
      quantity: item.quantity.value,
      unitPrice: item.unitPrice.amount,
      unitFeeAmount: item.unitFeeAmount.amount,
      unitTaxAmount: item.unitTaxAmount.amount,
      feeType: item.feeType,
      appliedFees: item.appliedFees,
    }));
  }

  findTicketFeeCalculationResult(ticketTypeId: string, optionId: string): Maybe<TicketFeeCalculationResult> {
    return Maybe.fromValue(this._fees.tickets.toArray()
      .find(item => item.ticketTypeId.value === ticketTypeId && item.optionId === optionId && item.feeType === FeeCalculationResultFeeType.ALL));
  }

  setPaymentId(paymentId: string): this {
    this._paymentId = Maybe.some(UniqueEntityID.build(paymentId));

    return this;
  }

  setOrderId(orderId: string): this {
    this._orderId = Maybe.some(UniqueEntityID.build(orderId));

    return this;
  }

  setTicketFees(ticketFees: TicketFeeCalculationResult[]): this {
    this._fees.tickets = Collection.build(ticketFees, 'optionId');

    return this;
  }

  addFeesResponse(feesResponse: FeesResponse, ticketTypeId: string, optionId: string): this {
    const ticketFees = this._fees.tickets.toArray();

    const ticketFee = ticketFees
      .find(item => item.ticketTypeId.value === ticketTypeId && item.optionId === optionId && item.feeType === FeeCalculationResultFeeType.ALL);

    if (ticketFee) {
      ticketFee.quantity = ticketFee.quantity.add(1);

      return this.setTicketFees(ticketFees);
    }

    const currency = feesResponse.currency;
    const unitPriceOrError = Money.build({ amount: feesResponse.amounts.subTotal, currency });

    if (unitPriceOrError.isLeft()) {
      return this;
    }

    const unitFeeAmountOrError = Money.build({ amount: feesResponse.amounts.fees, currency });

    if (unitFeeAmountOrError.isLeft()) {
      return this;
    }

    const unitTaxAmountOrError = Money.build({ amount: feesResponse.amounts.taxes, currency });

    if (unitTaxAmountOrError.isLeft()) {
      return this;
    }

    const newTicketFee: TicketFeeCalculationResult = {
      ticketTypeId: UniqueEntityID.build(ticketTypeId),
      optionId,
      quantity: FvNumber.build(1),
      unitPrice: unitPriceOrError.value,
      unitFeeAmount: unitFeeAmountOrError.value,
      unitTaxAmount: unitTaxAmountOrError.value,
      feeType: FeeCalculationResultFeeType.ALL,
      appliedFees: feesResponse,
    };

    return this.setTicketFees([...ticketFees, newTicketFee]);
  }

  toPrimitives(): FeeCalculationResultPrimitives {
    return {
      id: this.id,
      paymentId: this.paymentId,
      orderId: this.orderId,
      fees: this.fees,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
