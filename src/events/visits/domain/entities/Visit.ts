import {
  AggregateRoot,
  FvDate,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { defaultStamps, stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { Device } from '@/cross-cutting/domain/contracts/CommonContracts';
import type { CreateEntityPrimitives } from '@/cross-cutting/domain/contracts/CreateEntityPrimitives';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  DatePrimitive,
  ECountryCode,
  EGender,
  Either,
  ELanguagesCodes,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

type VisitCustomer = {
  language: Maybe<ELanguagesCodes>;
  country: Maybe<ECountryCode>;
  gender: Maybe<EGender>;
  birthDate: Maybe<FvDate>;
};

type VisitCustomerPrimitives = {
  language: Maybe<ELanguagesCodes>;
  country: Maybe<ECountryCode>;
  gender: Maybe<EGender>;
  birthDate: Maybe<DatePrimitive>;
};

export type VisitCustomerPrimitive = {
  language: Maybe<ELanguagesCodes>;
  country: Maybe<ECountryCode>;
  gender: Maybe<EGender>;
  birthDate: Maybe<DatePrimitive>;
};

export type VisitPrimitives = Primitives<Visit>;

export type CreateVisitPrimitives = CreateEntityPrimitives<Omit<VisitPrimitives, 'duration'>>;

export type EventVisitedResponseEither = Either<MapperError | NotFoundError, true>;

export class Visit extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    readonly applicationId: string,
    private readonly _organizationId: Maybe<UniqueEntityID>,
    private readonly _eventId: Maybe<UniqueEntityID>,
    readonly linkCode: Maybe<string>,
    private readonly _userId: Maybe<UniqueEntityID>,
    readonly device: Device,
    readonly duration: number,
    private readonly _customer: VisitCustomer,
    readonly idx: string,
    private readonly _referrerId: Maybe<UniqueEntityID>,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static create(primitives: CreateVisitPrimitives): Visit {
    const id = UniqueEntityID.create();

    const organizationId = primitives.organizationId.map(item => UniqueEntityID.build(item));
    const eventId = primitives.eventId.map(item => UniqueEntityID.build(item));
    const userId = primitives.userId.map(item => UniqueEntityID.build(item));
    const referrerId = primitives.referrerId.map(item => UniqueEntityID.build(item));
    const linkCode = primitives.linkCode.map(item => item);
    const duration = 5;

    const customer: VisitCustomer = {
      ...primitives.customer,
      language: primitives.customer.language.map(item => item),
      country: primitives.customer.country.map(item => item),
      gender: primitives.customer.gender.map(item => item),
      birthDate: primitives.customer.birthDate.map(item => FvDate.create(item)),
    };

    const stamps = stampValueObjects(defaultStamps());

    const entity = new Visit(
      id,
      primitives.applicationId,
      organizationId,
      eventId,
      linkCode,
      userId,
      primitives.device,
      duration,
      customer,
      primitives.idx,
      referrerId,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return entity;
  }

  get organizationId(): Maybe<IdPrimitive> {
    return this._organizationId.map(item => item.value);
  }

  get eventId(): Maybe<IdPrimitive> {
    return this._eventId.map(item => item.value);
  }

  get userId(): Maybe<IdPrimitive> {
    return this._userId.map(item => item.value);
  }

  get customer(): VisitCustomerPrimitives {
    return {
      ...this._customer,
      birthDate: this._customer.birthDate.map(item => item.toPrimitive()),
    };
  }

  get referrerId(): Maybe<IdPrimitive> {
    return this._referrerId.map(item => item.value);
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  toPrimitives(): VisitPrimitives {
    return {
      id: this.id,
      applicationId: this.applicationId,
      organizationId: this.organizationId,
      eventId: this.eventId,
      linkCode: this.linkCode.map(item => item),
      userId: this.userId,
      device: this.device,
      duration: this.duration,
      customer: this.customer,
      idx: this.idx,
      referrerId: this.referrerId,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
