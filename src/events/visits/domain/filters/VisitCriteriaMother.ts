import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { ApplicationIdFilter } from '@/cross-cutting/domain/filters/ApplicationIdFilter';
import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { IdxFilter } from '@/cross-cutting/domain/filters/IdxFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class VisitCriteriaMother {
  static inEventToMatch(
    eventId: UniqueEntityID,
    referrerId: UniqueEntityID,
    applicationId: string,
    idx: string,
  ): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(eventId));
    filters.add(EventIdFilter.buildEqual(referrerId));
    filters.add(ApplicationIdFilter.buildEqual(applicationId));
    filters.add(IdxFilter.buildEqual(idx));

    return Criteria.build(filters);
  }
}
