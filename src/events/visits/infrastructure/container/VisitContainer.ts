import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { VisitDependencyIdentifier } from '../../domain/dependencyIdentifier/VisitDependencyIdentifier';
import { VisitMongoRepository } from '../database/repositories/VisitMongoRepository';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const VisitContainer = {
  register: (): void => {
    container.register(VisitDependencyIdentifier.VisitRepository, {
      useFactory: instanceCachingFactory(() => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new VisitMongoRepository(dbConnection);
      }),
    });
  },
};
