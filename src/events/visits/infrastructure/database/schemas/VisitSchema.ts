import { FvDate, Gender } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

const visitSchema = {
  _id: { type: String },
  negocio_id: {
    type: String,
    index: true,
  },
  evento_id: {
    type: String,
    index: true,
  },
  link_codigo: {
    type: String,
    index: true,
    default: '',
  },
  usuario_id: { type: String },
  browser: { type: String },
  language: { type: String },
  country: { type: String },
  os: { type: String },
  device: { type: String },
  duracion: {
    type: Number,
    default: 0,
  },
  genero: {
    type: String,
    enum: Gender.values(),
  },
  fecha_nacimiento: { type: Number },
  idx: {
    type: String,
    index: true,
  },
  referente_id: {
    type: String,
    index: true,
  },
  aplicacion_id: {
    type: String,
    required: true,
    index: true,
  },
  created_at: {
    type: Number,
    default: FvDate.create().toMilliseconds(),
    index: true,
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_at: {
    type: Number,
    default: FvDate.create().toMilliseconds(),
    index: true,
  },
  updated_by: {
    type: String,
    default: '',
  },
  removed_at: {
    type: Number,
    default: 0,
    index: true,
  },
  removed_by: {
    type: String,
    default: '',
    select: false,
  },
};

export const VisitSchema = new Schema(visitSchema);

VisitSchema.index({
  evento_id: 1,
  referente_id: 1,
  removed_at: 1,
});

VisitSchema.index({
  evento_id: 1,
  removed_at: 1,
});
