import { FvDate } from '@discocil/fv-domain-library/domain';

import type { Visit } from '@/events/visits/domain/entities/Visit';
import type { VisitSchemaType } from '../schemas/VisitSchemaType';

export class VisitSchemaMapper {
  static execute(visit: Visit): VisitSchemaType {
    const { customer } = visit;

    return {
      _id: visit.id,
      negocio_id: visit.organizationId.fold(() => undefined, item => item),
      evento_id: visit.eventId.fold(() => undefined, item => item),
      link_codigo: visit.linkCode.fold(() => '', item => item),
      usuario_id: visit.userId.fold(() => undefined, item => item),
      browser: visit.device.browser ?? undefined,
      os: visit.device.os ?? undefined,
      device: visit.device.device ?? undefined,
      duracion: visit.duration,
      language: customer.language.fold(() => undefined, item => item),
      country: customer.country.fold(() => undefined, item => item),
      genero: customer.gender.fold(() => undefined, item => item),
      fecha_nacimiento: customer.birthDate.fold(() => undefined, item => FvDate.create(item).toSeconds()),
      idx: visit.idx,
      referente_id: visit.referrerId.fold(() => undefined, item => item),
      aplicacion_id: visit.applicationId,
      created_at: visit.createdAt,
      created_by: visit.createdBy,
      updated_at: visit.updatedAt,
      updated_by: visit.updatedBy,
      removed_at: visit.removedAt,
      removed_by: visit.removedBy,
    };
  }
}
