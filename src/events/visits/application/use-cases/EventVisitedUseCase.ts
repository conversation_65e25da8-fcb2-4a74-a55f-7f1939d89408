import {
  FvDate,
  left,
  Maybe,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { UserCriteriaMother } from '@/user/domain/filters/UserCriteriaMother';

import { Visit } from '../../domain/entities/Visit';

import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { EventVisitedDto } from '@/events/events/domain/contracts/EventVisitedContract';
import type { UserRepository } from '@/user/domain/contracts/UserRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { VisitRepository } from '../../domain/contracts/VisitRepository';
import type { EventVisitedResponseEither } from '../../domain/entities/Visit';

export class EventVisitedUseCase implements UseCase<EventVisitedDto, Promise<EventVisitedResponseEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly userRepository: UserRepository,
    private readonly visitRepository: VisitRepository,
  ) {}

  @contextualizeError()
  async execute(dto: EventVisitedDto): Promise<EventVisitedResponseEither> {
    const {
      eventId, referrerId, applicationId, idx, customer, device,
    } = dto;

    const eventCriteria = EventCriteriaMother.idToMatch(UniqueEntityID.build(eventId));
    const eventResult = await this.eventRepository.find(eventCriteria);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    const event = eventResult.value;

    if (referrerId.isDefined()) {
      const referrerCriteria = UserCriteriaMother.idToMatch(UniqueEntityID.build(referrerId.get()));
      const referrerResult = await this.userRepository.find(referrerCriteria);

      if (referrerResult.isLeft()) {
        return left(referrerResult.value);
      }
    }

    const visit = Visit.create({
      applicationId,
      idx,
      referrerId,
      eventId: Maybe.fromValue(event.id),
      organizationId: Maybe.none(),
      linkCode: Maybe.none(),
      userId: Maybe.none(),
      customer: {
        ...customer,
        birthDate: customer.birthDate.map(birthDate => FvDate.createFromSeconds(birthDate).toPrimitive()),
      },
      device,
    });

    await this.visitRepository.save(visit);

    return right(true);
  }
}
