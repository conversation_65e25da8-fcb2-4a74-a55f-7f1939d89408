import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FvDate } from '@discocil/fv-domain-library/domain';
import type { EventKeys } from '../entities/EventEntity';

class FilterField extends FilterFieldBase<EventKeys> {}

export class EndDateFilter {
  private static readonly field: EventKeys = 'endDate';

  static buildLessEqualThan(endDate: FvDate): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.lessEqualThan();
    const filterValue = FilterValue.build(endDate.toSeconds());

    return new Filter(field, operator, filterValue);
  }

  static buildGreaterEqualThan(endDate: FvDate): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.greaterEqualThan();
    const filterValue = FilterValue.build(endDate.toSeconds());

    return new Filter(field, operator, filterValue);
  }
}
