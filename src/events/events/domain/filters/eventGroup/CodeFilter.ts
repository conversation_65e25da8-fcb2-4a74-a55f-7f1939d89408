import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EventGroupKeys } from '../../entities/EventGroup';

class FilterField extends FilterFieldBase<EventGroupKeys> {}

export class CodeFilter {
  private static readonly field: EventGroupKeys = 'code';

  static build(value: string): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(value);

    return new Filter(field, operator, filterValue);
  }

  static buildIn(values: string[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();
    const filterValue = values.map(value => FilterValue.build(value));

    return new Filter(field, operator, filterValue);
  }
}
