import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { CodeFilter } from './CodeFilter';
import { EventIdsFilter } from './EventIdsFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class EventGroupCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static idsToMatch(id: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildIn(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static codeToMatch(code: string): Criteria {
    const filters = Filters.build();

    filters.add(CodeFilter.build(code));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static codesToMatch(codes: Set<string>): Criteria {
    const filters = Filters.build();

    filters.add(CodeFilter.buildIn(Array.from(codes)));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static codesToMatchForMicrosite(codes: Set<string>, organizations: Set<UniqueEntityID>): Criteria {
    const filters = Filters.build();

    filters.add(CodeFilter.buildIn(Array.from(codes)));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(OrganizationIdFilter.buildIn(Array.from(organizations)));

    return Criteria.build(filters);
  }

  static eventsToMatch(eventIds: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(EventIdsFilter.buildIn(eventIds.map(_id => _id.value)));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }
}
