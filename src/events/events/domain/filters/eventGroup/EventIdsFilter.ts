import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EventGroupKeys } from '../../entities/EventGroup';

class FilterField extends FilterFieldBase<EventGroupKeys> {}

export class EventIdsFilter {
  private static readonly field: EventGroupKeys = 'eventIds';

  static buildIn(value: string[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();
    const filterValues: FilterValue[] = value.map((value: string) => FilterValue.build(value));

    return new Filter(field, operator, filterValues);
  }
}
