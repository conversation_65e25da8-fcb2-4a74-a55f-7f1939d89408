import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FvDate } from '@discocil/fv-domain-library/domain';
import type { EventKeys } from '../entities/EventEntity';

class FilterField extends FilterFieldBase<EventKeys> {}

export class DateFilter {
  private static readonly field: EventKeys = 'date';

  static buildEquals(value: FvDate): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(value.toSeconds());

    return new Filter(field, operator, filterValue);
  }
}
