import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FvDate } from '@discocil/fv-domain-library/domain';
import type { EventKeys } from '../entities/EventEntity';

class FilterField extends FilterFieldBase<EventKeys> {}

export class StartDateFilter {
  static readonly field: EventKeys = 'startDate';

  static buildGreaterEqualThan(startDate: FvDate): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.greaterEqualThan();
    const filterValue = FilterValue.build(startDate.toSeconds());

    return new Filter(field, operator, filterValue);
  }

  static buildLessEqualThan(endDate: FvDate): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.lessEqualThan();
    const filterValue = FilterValue.build(endDate.toSeconds());

    return new Filter(field, operator, filterValue);
  }
}
