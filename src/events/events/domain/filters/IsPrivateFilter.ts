import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EventKeys } from '../entities/EventEntity';

class FilterField extends FilterFieldBase<EventKeys> {}

export class IsPrivateFilter {
  private static readonly field: EventKeys = 'isPrivate';

  static buildFalse(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.distinct();
    const filterValue = FilterValue.buildTrue();

    return new Filter(field, operator, filterValue);
  }

  static buildTrue(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.buildTrue();

    return new Filter(field, operator, filterValue);
  }
}
