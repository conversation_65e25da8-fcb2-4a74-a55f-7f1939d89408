import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EventKeys } from '../entities/EventEntity';

class FilterField extends FilterFieldBase<EventKeys> {}

export class ActiveFilter {
  private static readonly field: EventKeys = 'isActive';

  static buildTrue(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.buildTrue();

    return new Filter(field, operator, filterValue);
  }

  static buildFalse(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.buildFalse();

    return new Filter(field, operator, filterValue);
  }
}
