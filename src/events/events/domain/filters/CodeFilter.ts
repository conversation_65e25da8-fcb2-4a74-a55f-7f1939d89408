import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EventKeys } from '../entities/EventEntity';

class FilterField extends FilterFieldBase<EventKeys> {}

export class CodeFilter {
  private static readonly field: EventKeys = 'code';

  static buildEqual(code: string): Filter {
    const codeField = new FilterField(this.field);
    const codeOperator = FilterOperator.equal();
    const codeFilterValue = FilterValue.build(code);

    return new Filter(codeField, codeOperator, codeFilterValue);
  }
}
