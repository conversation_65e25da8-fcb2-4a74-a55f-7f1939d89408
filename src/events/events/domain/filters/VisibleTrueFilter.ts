import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EventKeys } from '../entities/EventEntity';

class FilterField extends FilterFieldBase<EventKeys> {}

export class VisibleFilter {
  private static readonly field: EventKeys = 'isVisible';

  static buildDistinctFalse(): Filter {
    const statusActiveField = new FilterField(this.field);
    const statusActiveOperator = FilterOperator.distinct();
    const statusActiveFilterValue = FilterValue.buildFalse();

    return new Filter(statusActiveField, statusActiveOperator, statusActiveFilterValue);
  }
}
