import {
  Criteria,
  Filters,
  Order,
  OrderTypes,
  Pagination,
} from '@discocil/fv-criteria-converter-library/domain';
import {
  FvBoolean,
  FvDate,
  Maybe,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { EventEntity } from '../entities/EventEntity';

import { ActiveFilter } from './ActiveFilter';
import { TokenFilter } from './bookingSpaceAvailability/TokenFilter';
import { CodeFilter } from './CodeFilter';
import { DateFilter } from './DateFilter';
import { EndDateFilter } from './EndDateFilter';
import { IsPrivateFilter } from './IsPrivateFilter';
import { StartDateFilter } from './StartDateFilter';
import { VisibleFilter } from './VisibleTrueFilter';

import type { Filter, PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventKeys } from '../entities/EventEntity';

export class EventCriteriaMother {
  private static readonly defaultOrderKey: EventKeys = 'startDate';

  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(ActiveFilter.buildTrue());

    return Criteria.build(filters);
  }

  static findForMicrositeByIdToMatch(id: UniqueEntityID, smsSale: boolean): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    if (!smsSale) {
      this.smsSale().forEach(filter => filters.add(filter));
    }

    return Criteria.build(filters);
  }

  static findForMicrositeByCodeToMatch(value: string, smsSale: boolean): Criteria {
    const filters = Filters.build();

    filters.add(CodeFilter.buildEqual(value));
    filters.add(RemovedAtFilter.buildActive());

    if (!smsSale) {
      this.smsSale().forEach(filter => filters.add(filter));
    }

    return Criteria.build(filters);
  }

  static codeToMatch(value: string): Criteria {
    const filters = Filters.build();

    filters.add(CodeFilter.buildEqual(value));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static organizationsToMatch(
    organizationId: UniqueEntityID[],
    startDate: FvDate,
    endDate: FvDate,
    paginationOption?: PaginationOption,
    isPrivate?: boolean,
  ): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildIn(organizationId));
    filters.add(StartDateFilter.buildGreaterEqualThan(startDate));
    filters.add(StartDateFilter.buildLessEqualThan(endDate));
    filters.add(EndDateFilter.buildLessEqualThan(endDate));
    filters.add(EndDateFilter.buildGreaterEqualThan(startDate));
    filters.add(VisibleFilter.buildDistinctFalse());
    filters.add(ActiveFilter.buildTrue());
    filters.add(RemovedAtFilter.buildActive());

    if (FvBoolean.is(isPrivate)) {
      if (isPrivate) {
        filters.add(IsPrivateFilter.buildTrue());
      } else {
        filters.add(IsPrivateFilter.buildFalse());
      }
    }

    if (!paginationOption) {
      return Criteria.build(filters);
    }

    const {
      order: orderOption, page, perPage,
    } = paginationOption as PaginationOption<EventKeys>;

    const orderBy = this.getSortableKey(orderOption.by);
    const order = Order.fromValues<EventKeys>(orderBy, orderOption.type);

    const pagination = Pagination.build(page, perPage);

    return Criteria.build(filters, order, pagination);
  }

  private static smsSale(): Filter[] {
    const filters: Filter[] = [];

    const statusActiveFilters = ActiveFilter.buildTrue();
    const visibleFilters = VisibleFilter.buildDistinctFalse();

    filters.push(statusActiveFilters);
    filters.push(visibleFilters);

    return filters;
  }

  static idsToMatch(ids: UniqueEntityID[], paginationOption?: PaginationOption, isPrivate?: boolean): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildIn(ids));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(ActiveFilter.buildTrue());

    if (FvBoolean.is(isPrivate)) {
      if (isPrivate) {
        filters.add(IsPrivateFilter.buildTrue());
      } else {
        filters.add(IsPrivateFilter.buildFalse());
      }
    }

    if (!paginationOption) {
      return Criteria.build(
        filters,
        Order.asc<EventKeys>(
          this.getSortableKey(Maybe.some(this.defaultOrderKey)),
        ),
      );
    }

    const {
      order: orderOption, page, perPage,
    } = paginationOption as PaginationOption<EventKeys>;

    const orderBy = orderOption.by.getOrElse(this.defaultOrderKey);
    const orderType = orderOption.type ?? OrderTypes.ASC;

    const order = Order.fromValues<EventKeys>(orderBy, orderType);
    const pagination = Pagination.build(page, perPage);

    return Criteria.build(filters, order, pagination);
  }

  static availableByMicrositeToMatch(
    organizationIds: Set<string>,
    paginationOption?: PaginationOption,
  ): Criteria {
    const _organizationIds = [...organizationIds].map((_id: string) => UniqueEntityID.build(_id));
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildIn(_organizationIds));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(StartDateFilter.buildGreaterEqualThan(FvDate.create()));
    filters.add(IsPrivateFilter.buildFalse());

    if (!paginationOption) {
      return Criteria.build(filters);
    }

    const {
      order: orderOption, page, perPage,
    } = paginationOption as PaginationOption<EventKeys>;

    const orderBy = this.getSortableKey(orderOption.by);
    const order = Order.fromValues<EventKeys>(orderBy, orderOption.type);
    const pagination = Pagination.build(page, perPage);

    return Criteria.build(filters, order, pagination);
  }

  static dateToMatch(date: FvDate): Criteria {
    const filters = Filters.build();

    const dateFilter = DateFilter.buildEquals(date);

    filters.add(dateFilter);

    return Criteria.build(filters);
  }

  static idAndOrganizationIdToMatch(id: UniqueEntityID, organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(id));
    filters.add(OrganizationIdFilter.buildEqual(organizationId));

    return Criteria.build(filters);
  }

  static bookingSpaceAvailabilityByTokenToMatch(token: IdPrimitive): Criteria {
    const filters = Filters.build();

    filters.add(TokenFilter.buildEqual(token));

    return Criteria.build(filters);
  }

  private static getSortableKey(orderByIncoming: Maybe<EventKeys>): EventKeys {
    let orderBy = orderByIncoming.getOrElse(StartDateFilter.field);

    if (!EventEntity.isKeySortable(orderBy)) {
      orderBy = StartDateFilter.field;
    }

    return orderBy;
  }
}
