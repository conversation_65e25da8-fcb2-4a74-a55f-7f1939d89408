import type { Device } from '@/cross-cutting/domain/contracts/CommonContracts';
import type {
  ECountryCode, EGender,
  ELanguagesCodes,
  Maybe,
} from '@discocil/fv-domain-library/domain';

type VisitCustomerDto = {
  language: Maybe<ELanguagesCodes>;
  country: Maybe<ECountryCode>;
  gender: Maybe<EGender>;
  birthDate: Maybe<number>;
};

export type EventVisitedDto = {
  readonly eventId: string;
  readonly referrerId: Maybe<string>;
  readonly applicationId: string;
  readonly idx: string;
  readonly customer: VisitCustomerDto;
  readonly device: Device;
};
