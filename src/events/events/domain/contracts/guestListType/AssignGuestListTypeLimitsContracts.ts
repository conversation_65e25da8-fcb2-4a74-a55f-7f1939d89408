import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { GuestListType } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type {
  Either,
  InvalidArgumentError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

export type AssignGuestTypeLimitsRequest = {
  readonly guestListType: GuestListType;
  readonly channel: MicrositeChannel;
};

export type AssignGuestTypeLimits = {
  readonly maximum: number;
  readonly originalMaximum: number;
  readonly available: number;
};

export type AssignGuestTypeLimitsEither = Either<
  InvalidArgumentError
  | InvalidFieldError
  | MapperError
  | NotFoundError
  | UnexpectedError,
  AssignGuestTypeLimits
>;

export interface IAssignGuestTypeLimitsService {
  execute: (dto: AssignGuestTypeLimitsRequest) => Promise<AssignGuestTypeLimitsEither>;
}
