import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { GuestListType } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type {
  Either,
  InvalidArgumentError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

export type GuestListTypeLimitsRequest = {
  readonly guestListType: GuestListType;
  readonly channel: MicrositeChannel;
};

export type GuestListTypeLimitsResponse = {
  readonly maximum: number;
  readonly totalSales: number;
  readonly available: number;
};

export type GuestListTypeLimitsEither = Either<
  InvalidArgumentError
  | InvalidFieldError
  | MapperError
  | NotFoundError
  | UnexpectedError,
  GuestListTypeLimitsResponse
>;

export interface IGetGuestListTypeLimitsService {
  execute: (dto: GuestListTypeLimitsRequest) => Promise<GuestListTypeLimitsEither>;
}
