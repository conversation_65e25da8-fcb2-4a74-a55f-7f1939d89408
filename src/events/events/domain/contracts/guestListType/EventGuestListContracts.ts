import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { GuestListTypes } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { BadSlugError } from '@/microsite/domain/errors/BadSlugError';
import type { PaginationDto, PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  IdPrimitive,
  InvalidArgumentError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '../../entities/EventEntity';
import type { ServiceNotAvailableError } from '../../errors/ServiceNotAvailableError';
import type { AssignGuestTypeLimits } from './AssignGuestListTypeLimitsContracts';

export type GuestListTypeTotalPrice = {
  readonly min: number;
  readonly max: number;
};

export type GuestListTypeTotalPrices = Map<IdPrimitive, GuestListTypeTotalPrice>;
export type GuestListTypesLimits = Map<IdPrimitive, AssignGuestTypeLimits>;

export type GuestListTypesAvailableRequest = PaginationDto & {
  readonly event: EventEntity;
  readonly language: string;
  readonly channel: MicrositeChannel;
};

export type GuestListTypesAvailable = Omit<SearchEventGuestListsResponse, 'event'>;

export type GuestListTypesAvailableEither = Either<
  InvalidArgumentError
  | InvalidFieldError
  | MapperError
  | NotFoundError
  | UnexpectedError,
  GuestListTypesAvailable
>;

export interface IGetGuestListTypesAvailableService {
  execute: (dto: GuestListTypesAvailableRequest) => Promise<GuestListTypesAvailableEither>;
}

export type SearchEventGuestListsResponse = PaginationMetadataResponse & {
  readonly event: EventEntity;
  readonly guestListTypes: GuestListTypes;
  readonly prices: GuestListTypeTotalPrices;
  readonly limits: GuestListTypesLimits;
};

export type SearchEventGuestListError = BadSlugError
  | InvalidArgumentError
  | InvalidFieldError
  | MapperError
  | NotFoundError
  | ServiceNotAvailableError
  | UnexpectedError;

export type SearchEventGuestListsEither = Either<
  SearchEventGuestListError,
  SearchEventGuestListsResponse
>;
