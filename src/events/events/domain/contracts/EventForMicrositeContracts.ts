import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type { FvDate } from '@discocil/fv-domain-library/domain';
import type { EventsEither } from '../entities/EventEntity';

export type EventSearchFactoryDto = {
  readonly micrositeChannel: MicrositeChannel;
  readonly organizationSlugs: Set<string>;
  readonly eventGroupCodes: Set<string>;
  readonly startDate: FvDate;
  readonly endDate: FvDate;
  readonly pagination: PaginationOption;
  readonly isPrivate: boolean;
};

export interface EventForMicrositeSearcher {
  execute(dto: EventSearchFactoryDto): Promise<EventsEither>;
}
