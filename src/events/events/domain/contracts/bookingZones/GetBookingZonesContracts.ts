import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  InvalidDateError,
  MoneyError,
  NotFoundError,
} from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '../../entities/EventEntity';
import type { SearchEventBookingZonesResponse } from './EventBookingsZones';

export type GetBookingZonesEither = Either<MapperError | NotFoundError | InvalidDateError | MoneyError, SearchEventBookingZonesResponse>;

export interface IGetBookingZonesServices {
  execute: (event: EventEntity, pagination?: PaginationOption) => Promise<GetBookingZonesEither>;
}
