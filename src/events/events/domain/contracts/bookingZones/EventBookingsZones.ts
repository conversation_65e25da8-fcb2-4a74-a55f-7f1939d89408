import type { BookingTypeCollection } from '@/bookings/bookingTypes/domain/entities/BookingTypeCollection';
import type { BookingZones } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { BadSlugError } from '@/microsite/domain/errors/BadSlugError';
import type { PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
} from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '../../entities/EventEntity';
import type { ServiceNotAvailableError } from '../../errors/ServiceNotAvailableError';

export type SearchEventBookingZonesResponse = PaginationMetadataResponse & {
  readonly event: EventEntity;
  readonly zones: BookingZones;
  readonly typesByZones: BookingTypeCollection;
};

export type SearchEventBookingZonesEither = Either<
  ServiceNotAvailableError | MapperError | NotFoundError | InvalidArgumentError | BadSlugError | MoneyError,
  SearchEventBookingZonesResponse
>;
