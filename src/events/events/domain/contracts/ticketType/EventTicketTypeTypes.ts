import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { BadSlugError } from '@/microsite/domain/errors/BadSlugError';
import type { TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '../../entities/EventEntity';
import type { ServiceNotAvailableError } from '../../errors/ServiceNotAvailableError';

export type SearchEventTicketsTypeResponse = PaginationMetadataResponse & {
  readonly event: EventEntity;
  readonly ticketTypes: TicketTypes;
  readonly language: string;
};

export type SearchEventTicketTypesError = ServiceNotAvailableError
  | InvalidArgumentError
  | MapperError
  | NotFoundError
  | UnexpectedError
  | BadSlugError
  | MoneyError;

export type SearchEventTicketsTypesEither = Either<
  SearchEventTicketTypesError,
  SearchEventTicketsTypeResponse
>;
