import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class EventDatesIncompatible extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_FIELD;

  static startDateInvalid(request: ErrorMethodRequest): EventDatesIncompatible {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = `Event invalid dates: Start date should not be greater than end date`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static dateRangeInvalid(request: ErrorMethodRequest): EventDatesIncompatible {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'Date range must not be greater than one year';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
