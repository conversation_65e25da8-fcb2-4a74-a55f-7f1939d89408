import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class EventIsCancelled extends FvError {
  static readonly defaultCause = EErrorKeys.ENTITY_NOT_FOUND;

  static build(request: ErrorMethodRequest): EventIsCancelled {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'Event is cancelled';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
