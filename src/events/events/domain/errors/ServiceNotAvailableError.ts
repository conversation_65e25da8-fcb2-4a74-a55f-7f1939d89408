import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class ServiceNotAvailableError extends FvError {
  static readonly defaultCause = EErrorKeys.RATE_NOT_EXIST_FOR_EVENT;

  static build(request: ErrorMethodRequest): ServiceNotAvailableError {
    const exceptionMessage = 'Service not available for the event';

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
