import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class SearchEventErrors extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_FIELD;

  static slugIsRequired(request: ErrorMethodRequest): SearchEventErrors {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = `Slug is required`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static dateRangeIsRequired(request: ErrorMethodRequest): SearchEventErrors {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = `Start and end date are required`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
