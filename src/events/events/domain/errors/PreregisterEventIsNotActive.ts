import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { FvErrorRequest } from '@discocil/fv-domain-library/domain';

export class PreregisterEventIsNotActive extends FvError {
  static readonly defaultCause = EErrorKeys.ENTITY_NOT_FOUND;

  static build(request: FvErrorRequest): PreregisterEventIsNotActive {
    const exceptionMessage = 'Preregister is not active for the event';

    const {
      context, message, error, data, target,
    } = request;

    return new this(
      message ?? exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
