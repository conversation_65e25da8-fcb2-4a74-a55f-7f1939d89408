import { Collection } from '@discocil/fv-domain-library/domain';

import type { CollectionKey, EAtmosphere } from '@discocil/fv-domain-library/domain';

export class AtmosphereCollection<T = EAtmosphere> extends Collection<T> {
  static override new<T = EAtmosphere>(key?: CollectionKey<T>): AtmosphereCollection<T> {
    return new this<T>(key);
  }

  static fromPrimitives(primitives: EAtmosphere[] | Set<EAtmosphere>): AtmosphereCollection<EAtmosphere> {
    const collection = new AtmosphereCollection<EAtmosphere>();

    primitives.forEach(item => collection.add(item));

    return collection;
  }

  intersection(collection: AtmosphereCollection<T>): this {
    const setA = collection.uniques();
    const setB = this.uniques();
    const intersection = setA.intersection(setB);

    const primitives = Array.from(intersection);

    return AtmosphereCollection.fromPrimitives(primitives as EAtmosphere[]) as this;
  }
}
