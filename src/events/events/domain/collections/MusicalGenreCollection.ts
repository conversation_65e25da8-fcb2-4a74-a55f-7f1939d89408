import { Collection } from '@discocil/fv-domain-library/domain';

import type { Collection<PERSON>ey, EMusicalGenres } from '@discocil/fv-domain-library/domain';

export class MusicalGenreCollection<T = EMusicalGenres> extends Collection<T> {
  static override new<T = EMusicalGenres>(key?: CollectionKey<T>): MusicalGenreCollection<T> {
    return new this<T>(key);
  }

  static fromPrimitives(primitives: EMusicalGenres[] | Set<EMusicalGenres>): MusicalGenreCollection<EMusicalGenres> {
    const collection = new MusicalGenreCollection<EMusicalGenres>();

    primitives.forEach(item => collection.add(item));

    return collection;
  }

  intersection(collection: MusicalGenreCollection<T>): this {
    const setA = collection.uniques();
    const setB = this.uniques();
    const intersection = setA.intersection(setB);

    const primitives = Array.from(intersection);

    return MusicalGenreCollection.fromPrimitives(primitives as EMusicalGenres[]) as this;
  }
}
