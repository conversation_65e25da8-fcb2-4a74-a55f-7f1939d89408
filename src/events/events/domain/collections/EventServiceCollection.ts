import { Collection } from '@discocil/fv-domain-library/domain';

import type { CollectionKey, EEventServices } from '@discocil/fv-domain-library/domain';

export class EventServiceCollection<T = EEventServices> extends Collection<T> {
  static override new<T = EEventServices>(key?: CollectionKey<T>): EventServiceCollection<T> {
    return new this<T>(key);
  }


  static fromPrimitives(primitives: EEventServices[] | Set<EEventServices>): EventServiceCollection<EEventServices> {
    const collection = new EventServiceCollection<EEventServices>();

    primitives.forEach(item => collection.add(item));

    return collection;
  }

  intersection(collection: EventServiceCollection<T>): this {
    const setA = collection.uniques();
    const setB = this.uniques();
    const intersection = setA.intersection(setB);

    const primitives = Array.from(intersection);

    return EventServiceCollection.fromPrimitives(primitives as EEventServices[]) as this;
  }
}
