import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { EventCriteriaMother } from '../filters/EventCriteriaMother';

import { GetActiveHostsService } from './GetActiveHostsService';

import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { User } from '@/user/domain/entities/User';
import type { Criteria, PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type { FvDate } from '@discocil/fv-domain-library/domain';
import type { EventForMicrositeSearcher, EventSearchFactoryDto } from '../contracts/EventForMicrositeContracts';
import type { EventRepository } from '../contracts/EventRepository';
import type { EventsEither } from '../entities/EventEntity';

type GetCriteriaRequest = {
  readonly micrositeChannel: MicrositeChannel;
  readonly startDate: FvDate;
  readonly endDate: FvDate;
  readonly pagination: PaginationOption;
  readonly isPrivate: boolean;
};

export class EventsForMicrositeReferrerService implements EventForMicrositeSearcher {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly organizationRepository: OrganizationRepository,
  ) { }

  @contextualizeError()
  async execute(dto: EventSearchFactoryDto): Promise<EventsEither> {
    const {
      micrositeChannel,
      startDate,
      endDate,
      pagination,
      isPrivate,
    } = dto;

    const criteria = await this.getCriteria({
      micrositeChannel,
      startDate,
      endDate,
      pagination,
      isPrivate,
    });

    return await this.eventRepository.search(criteria);
  }

  private async getCriteria(request: GetCriteriaRequest): Promise<Criteria> {
    const {
      micrositeChannel: { channel, organizations },
      startDate,
      endDate,
      pagination,
      isPrivate,
    } = request;

    const user = channel as User;

    let targetOrganizationIds: UniqueEntityID[] = [];

    if (organizations.size > 0) {
      for (const organizationId of organizations.keys()) {
        targetOrganizationIds.push(UniqueEntityID.build(organizationId));
      }
    } else {
      const hostIds = user.hosts.map(id => UniqueEntityID.build(id));
      const criteria = OrganizationCriteriaMother.idsToMatch(hostIds);
      const organizationResult = await this.organizationRepository.search(criteria);
      const getActiveHosts = new GetActiveHostsService(organizationResult);

      targetOrganizationIds = await getActiveHosts.execute();
    }

    return EventCriteriaMother.organizationsToMatch(targetOrganizationIds, startDate, endDate, pagination, isPrivate);
  }
}
