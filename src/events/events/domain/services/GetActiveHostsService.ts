import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';

import type { OrganizationsEither } from '@/organizations/organizations/domain/entities/Organization';

export class GetActiveHostsService {
  constructor(
    private readonly organizations: OrganizationsEither,
  ) {
  }

  @contextualizeError()
  async execute(): Promise<UniqueEntityID[]> {
    if (this.organizations.isLeft()) {
      return [];
    }

    const organizations: UniqueEntityID[] = [];

    this.organizations.value.forEach((organization) => {
      organizations.push(UniqueEntityID.build(organization.id));
    });

    return organizations;
  }
}
