import {
  FvDate,
  FvNumber,
  Maybe,
  right,
} from '@discocil/fv-domain-library/domain';

import type { Translator } from '@/cross-cutting/domain/contracts/TranslatorContracts';
import type { GuestListSummary } from '@/guestLists/guestListsTypes/domain/contracts/GuestListSummary';
import type { GuestListTypeOptionPrimitives } from '@/guestLists/guestListsTypes/domain/contracts/GuestListTypeOption';
import type {
  DatePrimitive,
  Either,
  InvalidArgumentError,
} from '@discocil/fv-domain-library/domain';

type GuestListSummaryServiceDto = {
  readonly startDate: DatePrimitive;
  readonly endDate: DatePrimitive;
  readonly tzName: Maybe<string>;
  readonly options: GuestListTypeOptionPrimitives[];
};

export class GuestListSummaryService {
  private readonly startDate: DatePrimitive;
  private readonly endDate: DatePrimitive;
  private readonly tzName: Maybe<string>;
  private readonly options: GuestListTypeOptionPrimitives[];

  constructor(
    parameters: GuestListSummaryServiceDto,
    private readonly translator: Translator,
  ) {
    this.startDate = parameters.startDate;
    this.endDate = parameters.endDate;
    this.tzName = parameters.tzName;
    this.options = parameters.options;
  }

  execute(): GuestListSummary[] {
    const summary: GuestListSummary[] = [];

    const options = this.options.sort((option1, option2) => {
      const dateFrom1 = FvDate.create(option1.dateFrom);
      const dateFrom2 = FvDate.create(option2.dateFrom);

      const price1_vo = FvNumber.build(option1.price);
      const price2_vo = FvNumber.build(option2.price);

      return !dateFrom1.equalTo(dateFrom2)
        ? dateFrom1.toMilliseconds() - dateFrom2.toMilliseconds()
        : price1_vo.subtract(price2_vo).toPrimitive();
    });

    options.forEach((option) => {
      const summaryOptionResult = this.summaryOption(option);

      if (summaryOptionResult.isRight()) {
        const summaryOption = summaryOptionResult.value;

        summary.push(summaryOption);
      }
    });

    return summary;
  }

  private summaryOption(option: GuestListTypeOptionPrimitives): Either<InvalidArgumentError, GuestListSummary> {
    const duration = this.headerStartSummary(option);
    const until = this.headerEndSummary(option);

    let content = Maybe.none<string>();

    if (option.content.isDefined()) {
      content = Maybe.fromValue(this.contentSummary(option));
    }

    let additionalInfo = Maybe.none<string>();

    if (option.additionalInfo.isDefined()) {
      additionalInfo = Maybe.fromValue(this.additionalInfoSummary(option));
    }

    const summary: GuestListSummary = {
      id: option.id,
      duration,
      until,
      content,
      additionalInfo,
    };

    return right(summary);
  }

  private headerStartSummary(option: GuestListTypeOptionPrimitives): string {
    const withoutDateFrom = FvDate.create(option.dateFrom).toMilliseconds() === 0;

    const dateTo = FvDate.create(option.dateTo);
    const newStartDateEvent = FvDate.create(this.startDate).addEpoch(dateTo);

    if (withoutDateFrom) {
      return newStartDateEvent.equalTo(this.endDate)
        ? this.translator.translate('common:listas_todo_evento')
        : this.translator.translate('common:listas_desde_inicio');
    }

    const dateFrom = FvDate.create(option.dateFrom);
    const timeFrom = FvDate.create(this.startDate).addEpoch(dateFrom);

    const formattedTimeTo = timeFrom.toFormat({
      style: 'timeShort',
      timeZone: this.tzName.fold(() => undefined, item => item),
    });

    return this.translator.translate(
      'common:listas_a_partir',
      { hora: formattedTimeTo },
    );
  }

  private headerEndSummary(option: GuestListTypeOptionPrimitives): string {
    const dateTo = FvDate.create(option.dateTo);
    const timeTo = FvDate.create(this.startDate).addEpoch(dateTo);

    const formattedTimeTo = timeTo.toFormat({
      style: 'timeShort',
      timeZone: this.tzName.fold(() => undefined, item => item),
    });

    this.translator.translate(
      'common:listas_hasta',
      { hora: formattedTimeTo },
    );

    return this.translator.translate('common:listas_hasta', { hora: formattedTimeTo });
  }

  private contentSummary(option: GuestListTypeOptionPrimitives): string {
    return this.translator.translate('common:incluye-contenido', { contenido: option.content.get() });
  }

  private additionalInfoSummary(option: GuestListTypeOptionPrimitives): string {
    return `${option.additionalInfo.get()}`;
  }
}
