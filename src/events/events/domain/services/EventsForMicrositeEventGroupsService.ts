import {
  left, NotFoundError, UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';

import { EventCriteriaMother } from '../filters/EventCriteriaMother';
import { EventGroupCriteriaMother } from '../filters/eventGroup/EventGroupCriteriaMother';

import type { EventForMicrositeSearcher, EventSearchFactoryDto } from '../contracts/EventForMicrositeContracts';
import type { EventRepository } from '../contracts/EventRepository';
import type { EventsEither } from '../entities/EventEntity';
import type { EventGroupRepository } from './../contracts/eventGroup/EventGroupRepository';

export class EventsForMicrositeEventGroupsService implements EventForMicrositeSearcher {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly eventGroupRepository: EventGroupRepository,
  ) { }

  @contextualizeError()
  async execute(dto: EventSearchFactoryDto): Promise<EventsEither> {
    const {
      eventGroupCodes, pagination, isPrivate,
    } = dto;

    const eventGroupCriteria = EventGroupCriteriaMother.codesToMatch(eventGroupCodes);
    const eventGroupResult = await this.eventGroupRepository.search(eventGroupCriteria);

    if (eventGroupResult.isLeft()) {
      return left(NotFoundError.build({ context: this.constructor.name }).notAutoContextualizable());
    }

    const eventGroups = eventGroupResult.value;
    const eventIds = new Set<UniqueEntityID>();

    for (const eventGroup of eventGroups.values()) {
      for (const eventId of eventGroup.eventIds) {
        eventIds.add(UniqueEntityID.build(eventId));
      }
    }

    const eventCriteria = EventCriteriaMother.idsToMatch(Array.from(eventIds), pagination, isPrivate);

    return await this.eventRepository.search(eventCriteria);
  }
}
