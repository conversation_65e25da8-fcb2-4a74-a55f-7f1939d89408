import { EMicrositeChannel } from '@discocil/fv-domain-library/domain';

import type { EventForMicrositeSearcher } from '../contracts/EventForMicrositeContracts';
import type { EventsForMicrositeEventGroupsService } from './EventsForMicrositeEventGroupsService';
import type { EventsForMicrositeOrganizationService } from './EventsForMicrositeOrganizationService';
import type { EventsForMicrositeReferrerService } from './EventsForMicrositeReferrerService';

export class EventForMicrositeFactory {
  constructor(
    private readonly eventForMicrositeOrganizationService: EventsForMicrositeOrganizationService,
    private readonly eventForMicrositeReferrerService: EventsForMicrositeReferrerService,
    private readonly eventForMicrositeEventGroupsService: EventsForMicrositeEventGroupsService,
  ) { }

  execute(micrositeChannelType: EMicrositeChannel, eventGroupCodes: Set<string>): EventForMicrositeSearcher {
    if (eventGroupCodes.size > 0) {
      return this.eventForMicrositeEventGroupsService;
    }

    if (micrositeChannelType === EMicrositeChannel.ORGANIZATION) {
      return this.eventForMicrositeOrganizationService;
    }

    return this.eventForMicrositeReferrerService;
  }
}
