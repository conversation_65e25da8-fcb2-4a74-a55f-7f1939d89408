import {
  Entity,
  FvDate,
  Maybe,
  UniqueEntityID,
  right,
} from '@discocil/fv-domain-library/domain';

import { EventPreregisterUser } from './EventPreregisterUser';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  DatePrimitive,
  Either,
  IdPrimitive,
  NotFoundError,
  Primitives,
  Properties,
} from '@discocil/fv-domain-library/domain';
import type {
  CreateEventPreregisterUser,
  EventPreregisterUserEither,
} from './EventPreregisterUser';

export type EventPreregisterEither = Either<MapperError | NotFoundError, EventPreregister>;

export type EventPreregisterPrimitives = Primitives<EventPreregister>;

export type EventPreregisterExternalPrimitives = Omit<
  Primitives<EventPreregister>, 'endDate'
> & {
  endDate: DatePrimitive | null;
};

export type EventPreregisterKeys = keyof Properties<EventPreregister>;

export type EventPreregisterUsers = Map<IdPrimitive, EventPreregisterUser>;

export class EventPreregister extends Entity {
  private readonly _users: EventPreregisterUsers = new Map<IdPrimitive, EventPreregisterUser>();
  private readonly _dirtyUsers: EventPreregisterUsers = new Map<IdPrimitive, EventPreregisterUser>();
  private _eventId = Maybe.none<UniqueEntityID>();

  private constructor(
    id: UniqueEntityID,
    private readonly _isActive: boolean,
    private readonly _endDate: Maybe<FvDate>,
  ) {
    super(id);
  }

  static build(primitives: EventPreregisterPrimitives): EventPreregisterEither {
    const id = UniqueEntityID.build(primitives.id);

    let endDate = Maybe.none<FvDate>();

    if (primitives.endDate.isDefined()) {
      endDate = Maybe.some(FvDate.create(primitives.endDate.get()));
    }

    const entity = new EventPreregister(
      id,
      primitives.isActive,
      endDate,
    );

    return right(entity);
  }

  private isEndDateActive(): boolean {
    if (this._endDate.isEmpty()) {
      return true;
    }

    return this._endDate.get().isFuture();
  }

  registerUser(primitives: CreateEventPreregisterUser): EventPreregisterUserEither {
    const userOrError = EventPreregisterUser.create({
      isRemarketing: primitives.isRemarketing,
      fields: primitives.fields,
      preregisterId: primitives.preregisterId.get().toString(),
    });

    if (userOrError.isRight()) {
      const user = userOrError.value;

      this._users.set(user.id, user);
      this._dirtyUsers.set(user.id, user);
    }

    return userOrError;
  }

  clearDirtyUsers(): void {
    this._dirtyUsers.clear();
  }

  getDirtyUsers(): EventPreregisterUsers {
    return new Map<IdPrimitive, EventPreregisterUser>(this._dirtyUsers);
  }

  hasDirtyUsers(): boolean {
    return this._dirtyUsers.size > 0;
  }

  getUsers(): EventPreregisterUsers {
    return this._users;
  }

  get isActive(): boolean {
    return this._isActive && this.isEndDateActive();
  }

  get endDate(): Maybe<DatePrimitive> {
    return this._endDate.map(date => date.toPrimitive());
  }

  getEventId(): Maybe<IdPrimitive> {
    return this._eventId.map(id => id.value);
  }

  setEventId(id: IdPrimitive): void {
    this._eventId = Maybe.some(UniqueEntityID.build(id));
  }

  toPrimitive(): EventPreregisterExternalPrimitives {
    return {
      id: this.id,
      isActive: this.isActive,
      endDate: this.endDate.fold(() => null, item => item),
    };
  }
}
