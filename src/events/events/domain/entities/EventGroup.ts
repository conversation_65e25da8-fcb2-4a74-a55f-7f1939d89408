import {
  Entity,
  UniqueEntityID,
  right,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  IdPrimitive,
  NotFoundError,
  Primitives,
  Properties,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type EventGroups = Map<IdPrimitive, EventGroup>;

export type EventGroupEither = Either<MapperError | NotFoundError, EventGroup>;
export type EventGroupsEither = Either<MapperError | NotFoundError, EventGroups>;

export type EventGroupPrimitives = Primitives<EventGroup>;

export type EventGroupKeys = keyof Properties<EventGroup>;

export class EventGroup extends Entity {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    private readonly _eventIds: Set<UniqueEntityID>,
    readonly code: string,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: EventGroupPrimitives): EventGroupEither {
    const id = UniqueEntityID.build(primitives.id);

    const eventUids = new Set<UniqueEntityID>();

    for (const _eventUid of primitives.eventIds) {
      eventUids.add(UniqueEntityID.build(_eventUid));
    }

    const stamps = stampValueObjects(primitives);

    const entity = new EventGroup(
      id,
      UniqueEntityID.build(primitives.organizationId),
      eventUids,
      primitives.code,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get eventIds(): Set<IdPrimitive> {
    const eventIds = new Set<IdPrimitive>();

    for (const eventUid of this._eventIds) {
      eventIds.add(eventUid.toPrimitive());
    }

    return eventIds;
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  toPrimitives(): EventGroupPrimitives {
    return {
      id: this.id,
      organizationId: this.organizationId,
      eventIds: this.eventIds,
      code: this.code,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
