import {
  Entity,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { defaultStamps, stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { CreateEntityPrimitives } from '@/cross-cutting/domain/contracts/CreateEntityPrimitives';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  Properties,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';
import type { PreregisterEventIsNotActive } from '../errors/PreregisterEventIsNotActive';

export type RegisterEventPreregisterUserResponse = {
  readonly id: IdPrimitive;
};

export type RegisterEventPreregisterUserEither = Either<MapperError | NotFoundError, RegisterEventPreregisterUserResponse>;

export type EventPreregisterUserEither = Either<MapperError | NotFoundError | PreregisterEventIsNotActive, EventPreregisterUser>;

export type EventPreregisterUserPrimitives = Primitives<EventPreregisterUser>;
export type CreateEventPreregisterUser = Omit<
  Pick<
    EventPreregisterUserPrimitives,
    'isRemarketing' | 'fields' | 'preregisterId'
  >,
  'preregisterId'
> & {
  preregisterId: Maybe<IdPrimitive>;
  isNewsletter: boolean;
};
export type CreateEventPreregisterUserPrimitives = CreateEntityPrimitives<EventPreregisterUserPrimitives>;

export type EventPreregisterUserKeys = keyof Properties<EventPreregisterUser>;

export class EventPreregisterUser extends Entity {
  private constructor(
    id: UniqueEntityID,
    private readonly _preregisterId: UniqueEntityID,
    readonly isRemarketing: boolean,
    readonly fields: Record<string, unknown>,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: EventPreregisterUserPrimitives): EventPreregisterUserEither {
    const id = UniqueEntityID.build(primitives.id);
    const preregisterId = UniqueEntityID.build(primitives.preregisterId);

    const stamps = stampValueObjects(primitives);

    const entity = new EventPreregisterUser(
      id,
      preregisterId,
      primitives.isRemarketing,
      primitives.fields,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  static create(primitives: CreateEventPreregisterUserPrimitives): EventPreregisterUserEither {
    const id = UniqueEntityID.create().toPrimitive();

    const entityResult = EventPreregisterUser.build({
      id,
      preregisterId: primitives.preregisterId,
      isRemarketing: primitives.isRemarketing,
      fields: primitives.fields,
      ...defaultStamps(),
    });

    if (entityResult.isLeft()) {
      return left(entityResult.value);
    }

    const entity = entityResult.value;

    return right(entity);
  }

  get preregisterId(): IdPrimitive {
    return this._preregisterId.toPrimitive();
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): IdPrimitive {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): IdPrimitive {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): IdPrimitive {
    return this._removedBy.toPrimitive();
  }
}
