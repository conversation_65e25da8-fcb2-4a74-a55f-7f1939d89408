import {
  AggregateRoot,
  EEventConfigurationKeys,
  EEventServices,
  ELanguagesCodes,
  FvDate,
  FvString,
  left,
  Maybe,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';
import { ServiceConverter } from '@/microsite/domain/value-objects/MicrositeService';

import { AtmosphereCollection } from '../collections/AtmosphereCollection';
import { EventServiceCollection } from '../collections/EventServiceCollection';
import { MusicalGenreCollection } from '../collections/MusicalGenreCollection';
import { BookingSpaceAvailabilityExpired } from '../errors/BookingSpaceAvailabilityExpired';
import { BookingSpaceAvailabilityNotFound } from '../errors/BookingSpaceAvailabilityNotFound';
import { PreregisterEventIsNotActive } from '../errors/PreregisterEventIsNotActive';
import { EventPreregisterUserCreatedDomainEvent } from '../events/EventPreregisterUserCreatedDomainEvent';
import { Hanger } from '../value-objects/Hanger';

import { EventPreregister } from './EventPreregister';

import type { BillingAddress, BillingAddresses } from '@/billingAddresses/domain/entities/BillingAddress';
import type { Images } from '@/cross-cutting/domain/contracts/Images';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Artists } from '@/events/artists/domain/entities/Artist';
import type { EventConfigurations } from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import type { GuestListTypes } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { LocationEntity } from '@/locations/domain/entities/LocationEntity';
import type { BadSlugError } from '@/microsite/domain/errors/BadSlugError';
import type { OrganizationConfiguration } from '@/organizations/organizationConfigurations/domain/entities/OrganizationConfiguration';
import type { Organization, Organizations } from '@/organizations/organizations/domain/entities/Organization';
import type { TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { CriteriaConverterError, PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  CheckError,
  CreatedAt,
  CreatedBy,
  DatePrimitive,
  EAtmosphere,
  ECurrency,
  EHanger,
  Either,
  EMicrositeServices,
  EMusicalGenres,
  IdPrimitive,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UnexpectedError,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';
import type { ServiceNotAvailableError } from '../errors/ServiceNotAvailableError';
import type { BookingSpaceAvailability, BookingSpaceAvailabilityExternalPrimitives } from './BookingSpaceAvailability';
import type {
  EventPreregisterExternalPrimitives,
  EventPreregisterPrimitives,
  EventPreregisterUsers,
} from './EventPreregister';
import type {
  CreateEventPreregisterUser,
  EventPreregisterUser,
  EventPreregisterUserEither,
} from './EventPreregisterUser';

export type EventImages = {
  readonly small: Maybe<string>;
  readonly medium: Maybe<string>;
};

export type InternalEventConfigurations = {
  readonly isQualityRequired: boolean;
  readonly isReceptionSingen: boolean;
  readonly isSittingEnabled: boolean;
  readonly organizerTerms: Maybe<string>;
  readonly rrppCanCancel: boolean;
  readonly hasEmailReconfirmation: boolean;
  readonly areFeesShownUpfront: boolean;
};

export type SetInternalEventConfigurations = {
  readonly eventConfigurations?: EventConfigurations;
  readonly organizationConfiguration: Partial<OrganizationConfiguration>;
};

export type FindEventResponse = {
  readonly event: EventEntity;
  readonly organization: Organization;
  readonly artists: Artists;
  readonly billingAddress: BillingAddress;
  readonly images: Maybe<EventImages>;
};

export type FindEventMetadataResponse = {
  readonly event: EventEntity;
  readonly organization: Organization;
  readonly artists: Maybe<Artists>;
  readonly ticketTypes: Maybe<TicketTypes>;
  readonly guestListTypes: Maybe<GuestListTypes>;
};

export type SearchPaginatedEvents = PaginationMetadataResponse & {
  readonly events: Events;
  readonly organizations: Organizations;
  readonly billingAddresses: BillingAddresses;
  readonly artists: Artists;
};

export type EventsPaginated = Pick<SearchPaginatedEvents, 'events' | 'pagination'>;

export type EventEither = Either<MapperError | NotFoundError, EventEntity>;
export type EventsEither = Either<MapperError | NotFoundError, EventsPaginated>;

export type FindEventEither = Either<MapperError | NotFoundError, FindEventResponse>;
export type SearchEventEither = Either<CheckError | MapperError | NotFoundError | BadSlugError | InvalidArgumentError, SearchPaginatedEvents>;
export type FindEventMetadataEither = Either<
  MapperError
  | NotFoundError
  | BookingSpaceAvailabilityNotFound
  | InvalidArgumentError
  | CriteriaConverterError
  | UnexpectedError
  | ServiceNotAvailableError
  | BadSlugError
  | MoneyError,
  FindEventMetadataResponse
>;

export type EventPrimitives = Primitives<EventEntity>;
export type EventPrimitivesBuild = EventPrimitives & {
  readonly preregister: Maybe<EventPreregisterPrimitives>;
};

export type Events = Map<IdPrimitive, EventEntity>;

export type EventKeys = keyof EventEntity;

export class EventEntity extends AggregateRoot {
  private configuration: InternalEventConfigurations = {
    organizerTerms: Maybe.none<string>(),
    isQualityRequired: false,
    isReceptionSingen: false,
    rrppCanCancel: false,
    isSittingEnabled: false,
    hasEmailReconfirmation: true,
    areFeesShownUpfront: true,
  };

  private blockedOrganizationIds = new Set<string>();
  private organization = Maybe.none<Organization>();
  private _location = Maybe.none<LocationEntity>();
  private _bookingSpaceAvailability = Maybe.none<BookingSpaceAvailability>();

  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    readonly slug: string,
    readonly name: string,
    readonly code: string,
    private readonly _locationId: UniqueEntityID,
    private readonly _date: FvDate,
    private readonly _startDate: FvDate,
    private readonly _endDate: FvDate,
    readonly isActive: boolean,
    private readonly _atmosphere: AtmosphereCollection,
    readonly applyCommissionEvent: boolean,
    readonly artists: string[],
    private readonly _canceled: Maybe<FvDate>,
    private _currency: ECurrency,
    readonly demo: boolean,
    readonly description: Maybe<string>,
    readonly day: number,
    readonly age: number,
    private readonly _dateLimitSale: FvDate,
    readonly photosNum: number,
    readonly functions: string[],
    private readonly _musicalGenres: MusicalGenreCollection,
    readonly image: Maybe<string>,
    private _imagePlan: Maybe<string>,
    readonly images: Maybe<Images>,
    readonly changeNamePrice: number,
    readonly municipality: string,
    readonly businessImage: string,
    readonly businessName: string,
    private readonly _perch: Hanger,
    readonly plan: number,
    readonly processed: boolean,
    readonly processing: boolean,
    readonly province: Maybe<string>,
    readonly provinceSlug: Maybe<string>,
    readonly qrMenuId: Maybe<string>,
    private _services: EventServiceCollection,
    readonly showImagePlan: boolean,
    readonly showLocation: boolean,
    readonly smsPrice: number,
    readonly smsSent: number,
    readonly tzName: Maybe<string>,
    readonly variableDiscocil: number,
    readonly isVisible: boolean,
    readonly parentalAuthorization: Maybe<string>,
    readonly isPrivate: boolean,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
    private readonly _preregister: Maybe<EventPreregister>,
  ) {
    super(id);
  }

  static build(primitives: EventPrimitivesBuild): EventEither {
    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const locationId = UniqueEntityID.build(primitives.locationId);

    const startDate = FvDate.create(primitives.startDate);
    const endDate = FvDate.create(primitives.endDate);
    const date = FvDate.create(primitives.date);
    const dateLimitSale = FvDate.create(primitives.dateLimitSale);

    const canceled = primitives.canceled.map(date => FvDate.create(date));
    const description = primitives.description.map(item => item);
    const image = primitives.image.map(item => item);
    const imagePlan = primitives.imagePlan.map(item => item);
    const images = primitives.images.map(item => item);
    const province = primitives.province.map(item => item);
    const provinceSlug = primitives.provinceSlug.map(item => item);
    const qrMenuId = primitives.qrMenuId.map(item => item);
    const tzName = primitives.tzName.map(item => item);
    const parentalAuthorization = primitives.parentalAuthorization.map(item => item);

    const stamps = stampValueObjects(primitives);

    const perch = Hanger.softBuild(primitives.perch);

    const atmosphere = AtmosphereCollection.fromPrimitives(primitives.atmosphere);
    const musicalGenres = MusicalGenreCollection.fromPrimitives(primitives.musicalGenres);
    const services = EventServiceCollection.fromPrimitives(primitives.services);

    let preregister = Maybe.none<EventPreregister>();

    if (primitives.preregister.isDefined()) {
      const preregisterOrError = EventPreregister.build(primitives.preregister.get());

      if (preregisterOrError.isRight()) {
        preregister = Maybe.some(preregisterOrError.value);
      }
    }

    const entity = new EventEntity(
      id,
      organizationId,
      primitives.slug,
      primitives.name,
      primitives.code,
      locationId,
      date,
      startDate,
      endDate,
      primitives.isActive,
      atmosphere,
      primitives.applyCommissionEvent,
      primitives.artists,
      canceled,
      primitives.currency,
      primitives.demo,
      description,
      primitives.day,
      primitives.age,
      dateLimitSale,
      primitives.photosNum,
      primitives.functions,
      musicalGenres,
      image,
      imagePlan,
      images,
      primitives.changeNamePrice,
      primitives.municipality,
      primitives.businessImage,
      primitives.businessName,
      perch,
      primitives.plan,
      primitives.processed,
      primitives.processing,
      province,
      provinceSlug,
      qrMenuId,
      services,
      primitives.showImagePlan,
      primitives.showLocation,
      primitives.smsPrice,
      primitives.smsSent,
      tzName,
      primitives.variableDiscocil,
      primitives.isVisible,
      parentalAuthorization,
      primitives.isPrivate,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
      preregister,
    );

    return right(entity);
  }

  static isKeySortable(key: EventKeys | undefined): boolean {
    if (!key) {
      return false;
    }

    const sortableKeys: Set<EventKeys> = new Set([
      'id',
      'date',
      'startDate',
      'endDate',
      'code',
      'isActive',
      'slug',
      'name',
    ]);

    return sortableKeys.has(key);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get locationId(): IdPrimitive {
    return this._locationId.toPrimitive();
  }

  get date(): DatePrimitive {
    return this._date.toPrimitive();
  }

  get startDate(): DatePrimitive {
    return this._startDate.toPrimitive();
  }

  get endDate(): DatePrimitive {
    return this._endDate.toPrimitive();
  }


  get atmosphere(): EAtmosphere[] {
    return this._atmosphere.toArray();
  }

  get musicalGenres(): EMusicalGenres[] {
    return this._musicalGenres.toArray();
  }

  get canceled(): Maybe<DatePrimitive> {
    return this._canceled.map(date => date.toPrimitive());
  }

  get dateLimitSale(): DatePrimitive {
    return this._dateLimitSale.toPrimitive();
  }

  get currency(): ECurrency {
    return this._currency;
  }

  get imagePlan(): Maybe<string> {
    return this._imagePlan;
  }

  get perch(): EHanger {
    return this._perch.toPrimitive();
  }

  get services(): EEventServices[] {
    return this._services.toArray();
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  isPreregisterActive(): boolean {
    if (!this.isActive || this.hasEnded()) {
      return false;
    }

    return this._preregister.fold(() => false, preregister => preregister.isActive);
  }

  getPreregisterPrimitives(): Maybe<EventPreregisterExternalPrimitives> {
    return this._preregister.map(preregister => preregister.toPrimitive());
  }

  getBookingSpaceAvailabilityPrimitives(): Maybe<BookingSpaceAvailabilityExternalPrimitives> {
    return this._bookingSpaceAvailability.map(bookingSpaceAvailability => bookingSpaceAvailability.toPrimitive());
  }

  isCancelled(): boolean {
    return this._canceled.isDefined();
  }

  isNoLongerSelling(): boolean {
    return this._dateLimitSale.isPast();
  }

  hasEnded(): boolean {
    return this.isCancelled() || this._endDate.isPast() || this.isNoLongerSelling();
  }

  getPreregisterId(): Maybe<string> {
    return this._preregister.map(preregister => preregister.id);
  }

  getStartDateInISO(): string {
    return this._startDate.toISODate();
  }

  getEndDateInISO(): string {
    return this._endDate.toISODate();
  }

  createPreregisterUser(primitives: CreateEventPreregisterUser, language: Maybe<ELanguagesCodes>): EventPreregisterUserEither {
    if (!this.isPreregisterActive()) {
      return left(PreregisterEventIsNotActive.build({
        context: this.constructor.name,
        data: {
          language,
          primitives: {
            isRemarketing: primitives.isRemarketing,
            fields: primitives.fields,
            preregisterId: primitives.preregisterId.getOrElse(''),
          },
        },
      }));
    }

    const userOrError = this._preregister.get().registerUser(primitives);

    if (userOrError.isRight()) {
      const user = userOrError.value;

      this.addDomainEvent(
        EventPreregisterUserCreatedDomainEvent.build({
          aggregateId: UniqueEntityID.build(user.id),
          attributes: {
            preregisterId: user.preregisterId,
            eventId: this.id,
            organizationId: this.organizationId,
            email: user.fields.email ?? '',
            userFirstName: user.fields.fullname,
            eventName: this.name,
            language: language.getOrElse(ELanguagesCodes.EN),
            shouldSendEmail: primitives.isNewsletter === false,
          },
        }),
      );
    }

    return userOrError;
  }

  hasDirtyPreregisterUsers(): boolean {
    return this._preregister.fold(
      () => false,
      preregister => preregister.hasDirtyUsers(),
    );
  }

  getDirtyPreregisterUsers(): EventPreregisterUsers {
    if (this._preregister.isEmpty()) {
      return new Map<IdPrimitive, EventPreregisterUser>();
    }

    const preregister = this._preregister.get();

    const dirtyUsers = preregister.getDirtyUsers();

    preregister.clearDirtyUsers();

    return dirtyUsers;
  }

  getLocation(): Maybe<LocationEntity> {
    return this._location;
  }

  getLocationAlias(): Maybe<string> {
    return this._location.fold(
      () => Maybe.none(),
      location => location.alias,
    );
  }

  setCurrency(value: ECurrency): this {
    this._currency = value;

    return this;
  }

  setBlockedOrganizations(blockedOrganizationIds: Set<string>): this {
    this.blockedOrganizationIds = blockedOrganizationIds;

    return this;
  }

  setBookingSpaceAvailability(value: BookingSpaceAvailability): this {
    this._bookingSpaceAvailability = Maybe.some(value);

    return this;
  }

  addBlockedOrganization(blockedOrganizationId: string): this {
    this.blockedOrganizationIds.add(blockedOrganizationId);

    return this;
  }

  getBlockedOrganizationIds(): Set<string> {
    return this.blockedOrganizationIds;
  }

  setOrganization(organization: Organization): this {
    this.organization = Maybe.some(organization);

    return this;
  }

  getOrganization(): Maybe<Organization> {
    return this.organization;
  }

  getConfiguration(): InternalEventConfigurations {
    return this.configuration;
  }

  getSmallImage(): Maybe<string> {
    if (this.images.isEmpty()) {
      return Maybe.none();
    }

    return this.images.get().small.map(item => item.versioned);
  }

  getMediumImage(): Maybe<string> {
    if (this.images.isEmpty()) {
      return Maybe.none();
    }

    return this.images.get().medium.map(item => item.versioned);
  }

  setConfiguration(request: SetInternalEventConfigurations): this {
    const { eventConfigurations, organizationConfiguration } = request;

    if (organizationConfiguration) {
      const isQualityRequired = organizationConfiguration.qualityRequired ?? this.configuration.isQualityRequired;
      const isReceptionSingen = organizationConfiguration.singenReception ?? this.configuration.isReceptionSingen;
      const organizerTerms = organizationConfiguration.termsConditions ?? this.configuration.organizerTerms;
      const hasEmailReconfirmation = organizationConfiguration.hasEmailReconfirmation ?? this.configuration.hasEmailReconfirmation;
      const areFeesShownUpfront = organizationConfiguration.areFeesShownUpfront ?? this.configuration.areFeesShownUpfront;

      this.configuration = {
        ...this.configuration,
        organizerTerms,
        isQualityRequired,
        isReceptionSingen,
        hasEmailReconfirmation,
        areFeesShownUpfront,
      };
    }

    if (eventConfigurations) {
      const organizerTermsConfig = eventConfigurations.get(EEventConfigurationKeys.ORGANIZER_TERMS);
      const isQualityRequiredConfig = eventConfigurations.get(EEventConfigurationKeys.QUALITY_REQUIRED);
      const isReceptionSingenConfig = eventConfigurations.get(EEventConfigurationKeys.RECEPTION_SINGEN);
      const rrppCanCancelConfig = eventConfigurations.get(EEventConfigurationKeys.RRPP_CAN_CANCEL);
      const isSittingEnabledConfig = eventConfigurations.get(EEventConfigurationKeys.SITTING_ENABLED);

      let organizerTerms = this.configuration.organizerTerms;

      if (organizerTermsConfig && FvString.is(organizerTermsConfig.value)) {
        const isEmptyValue = FvString.build(organizerTermsConfig.value).isEmpty();

        if (!isEmptyValue) {
          organizerTerms = Maybe.some((organizerTermsConfig.value));
        }
      }

      const isQualityRequired = Boolean(isQualityRequiredConfig?.value ?? this.configuration.isQualityRequired);
      const isReceptionSingen = Boolean(isReceptionSingenConfig?.value ?? this.configuration.isReceptionSingen);
      const rrppCanCancel = Boolean(rrppCanCancelConfig?.value ?? this.configuration.rrppCanCancel);
      const isSittingEnabled = Boolean(isSittingEnabledConfig?.value ?? this.configuration.isSittingEnabled);

      this.configuration = {
        ...this.configuration,
        organizerTerms,
        isQualityRequired,
        isReceptionSingen,
        rrppCanCancel,
        isSittingEnabled,
      };
    }

    return this;
  }

  getDateInSeconds(): number {
    return this._date.toSeconds();
  }

  getDateLimitSaleInSeconds(): number {
    return this._dateLimitSale.toSeconds();
  }

  getDateInISO(): string {
    return this._date.toISO();
  }

  getDateLimitSaleInISO(): string {
    return this._dateLimitSale.toISO();
  }

  getCanceledDateInSeconds(): Maybe<number> {
    return this._canceled.fold(
      () => Maybe.none(),
      item => Maybe.some(item.toSeconds()),
    );
  }

  isLocationDefined(): boolean {
    return this._location.isDefined();
  }

  shouldLocationBeShown(): boolean {
    return this.showLocation && this.isLocationDefined();
  }

  isEndDateLessThan(date: FvDate): boolean {
    return this._endDate.isLessThan(date);
  }

  setLocation(location: LocationEntity): this {
    const organization = UniqueEntityID.build(this.organizationId);

    location.setOrganizationId(organization);
    this._location = Maybe.some(location);

    return this;
  }

  setImagePlan(imagePlan: string): this {
    this._imagePlan = Maybe.fromValue(imagePlan);

    return this;
  };

  hasTickets(): boolean {
    return this.services.includes(EEventServices.TICKETS);
  }

  hasGuestLists(): boolean {
    return this.services.includes(EEventServices.LIST);
  }

  hasBookings(): boolean {
    const services = [EEventServices.BOOKED, EEventServices.BOOKINGS];

    return this.services.some(service => services.includes(service));
  }

  sortServices(micrositeServices: Set<EMicrositeServices>): void {
    if (micrositeServices.size === 0) {
      return;
    }

    const services = EventServiceCollection.new();
    const mapperConverter = new ServiceConverter();

    for (const _micrositeService of micrositeServices) {
      const foundServices = mapperConverter.get(_micrositeService);
      const intersectedItems = this._services.intersection(foundServices);

      services.merge(intersectedItems);
    }

    this._services = services;
  }

  getDateInMilliseconds(): number {
    return this._date.toMilliseconds();
  }

  hasBookingSpacesAvailability(): boolean {
    return this._bookingSpaceAvailability.isDefined();
  }

  bookingSpacesAvailabilityIsExpired(): boolean {
    return this._bookingSpaceAvailability.fold(
      () => false,
      item => item.isExpired(),
    );
  }

  getBookingSpacesAvailabilityCollaboratorId(): Maybe<IdPrimitive> {
    return this._bookingSpaceAvailability.map(item => item.collaboratorId);
  }

  ensureHasBookingSpaceAvailability(): Either<BookingSpaceAvailabilityNotFound, true> {
    if (this.hasBookingSpacesAvailability()) {
      return right(true);
    }

    return left(BookingSpaceAvailabilityNotFound.build({ context: this.constructor.name }));
  }

  ensureBookingSpaceAvailabilityIsAvailable(): Either<BookingSpaceAvailabilityExpired, true> {
    if (this.bookingSpacesAvailabilityIsExpired()) {
      return left(BookingSpaceAvailabilityExpired.build({ context: this.constructor.name }));
    }

    return right(true);
  }
}
