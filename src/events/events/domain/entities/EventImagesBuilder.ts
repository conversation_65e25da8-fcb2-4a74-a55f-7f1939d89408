import { Maybe } from '@discocil/fv-domain-library/domain';

import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { EventEntity, EventImages } from './EventEntity';

type EventImagesRequest = {
  readonly event: EventEntity;
  readonly organization: Organization;
};

interface IEventImagesBuilder {
  execute: (request: EventImagesRequest) => IEventImages;
}

interface IEventImages {
  areEmpty: () => boolean;
  getAll: () => EventImages;
  getSmall: () => Maybe<string>;
  getMedium: () => Maybe<string>;
}

export const EventImagesBuilder = (): IEventImagesBuilder => {
  let smallImage = Maybe.none<string>();
  let mediumImage = Maybe.none<string>();

  const areEmpty = (): boolean => {
    return !getSmall() && !getMedium();
  };

  const isValid = (image: string): boolean => {
    return !!(image.includes('cloudfront') || image.includes('cdn-cgi'));
  };

  const getSmall = (): Maybe<string> => {
    if (smallImage.isEmpty()) {
      return Maybe.none();
    }

    const _smallImage = smallImage.get();

    return isValid(_smallImage)
      ? Maybe.some(_smallImage)
      : Maybe.some(`w500/${_smallImage}`);
  };

  const getMedium = (): Maybe<string> => {
    if (mediumImage.isEmpty()) {
      return Maybe.none();
    }

    const _mediumImage = mediumImage.get();

    return isValid(_mediumImage)
      ? Maybe.some(_mediumImage)
      : Maybe.some(`w1000/${_mediumImage}`);
  };

  const getAll = (): EventImages => {
    return {
      small: getSmall(),
      medium: getMedium(),
    };
  };

  const setSmall = (image: string): void => {
    smallImage = Maybe.fromValue(image);
  };

  const setMedium = (image: string): void => {
    mediumImage = Maybe.fromValue(image);
  };

  const execute = (request: EventImagesRequest): IEventImages => {
    const { event, organization } = request;

    const eventImage = event.image.getOrElse('');

    const defaultSmallImage = organization.getCoverImageSmall().getOrElse(eventImage);
    const defaultMediumImage = organization.getCoverImageMedium().getOrElse(eventImage);

    const smallImage = event.getSmallImage().getOrElse(defaultSmallImage);
    const mediumImage = event.getMediumImage().getOrElse(defaultMediumImage);

    setSmall(smallImage);
    setMedium(mediumImage);

    return {
      getSmall,
      getMedium,
      areEmpty,
      getAll,
    };
  };

  return { execute };
};
