import { DomainEvent } from '@discocil/fv-domain-library/domain';

import type { DomainEventRequest } from '@discocil/fv-domain-library/domain';

export class EventPreregisterUserCreatedDomainEvent extends DomainEvent {
  static readonly EVENT_NAME: string = 'cli.event.preregister.user.created';

  static build(params: DomainEventRequest): EventPreregisterUserCreatedDomainEvent {
    return new this({
      ...params,
      type: this.EVENT_NAME,
    });
  }
}
