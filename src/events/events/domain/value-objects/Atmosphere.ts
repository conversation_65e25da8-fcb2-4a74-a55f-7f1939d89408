import {
  EAtmosphere,
  Either,
  FvEnum,
  FvNumber,
  InvalidArgumentError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { EnumLogSoft } from '@/cross-cutting/domain/value-objects/EnumLogSoft';

export class Atmosphere extends FvEnum<EAtmosphere> {
  private constructor(value: EAtmosphere) {
    super(value, Atmosphere.values());
  }

  static build(value: EAtmosphere): Either<InvalidArgumentError, Atmosphere> {
    return this.values().includes(value)
      ? right(new Atmosphere(value))
      : left(InvalidArgumentError.invalidValue({ context: this.constructor.name, target: value }));
  }

  @EnumLogSoft(() => Atmosphere.values())
  static softBuild(value: EAtmosphere): Atmosphere {
    return new Atmosphere(value);
  }

  static values(): EAtmosphere[] {
    return Object.values(EAtmosphere);
  }

  static random(): EAtmosphere {
    const values = Atmosphere.values();

    const randomIndex = FvNumber.random()
      .multiply(values.length)
      .floor()
      .toPrimitive();

    return values[randomIndex] as EAtmosphere;
  }
}
