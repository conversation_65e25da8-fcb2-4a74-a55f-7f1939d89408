import {
  <PERSON>Hanger,
  Either,
  FvEnum,
  InvalidArgumentError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { EnumLogSoft } from '@/cross-cutting/domain/value-objects/EnumLogSoft';

export class Hanger extends FvEnum<EHanger> {
  private constructor(value: EHanger) {
    super(value, Hanger.values());
  }

  static build(value: EHanger): Either<InvalidArgumentError, Hanger> {
    return this.values().includes(value)
      ? right(new Hanger(value))
      : left(InvalidArgumentError.invalidValue({
        context: this.constructor.name,
        target: value,
      }));
  }

  @EnumLogSoft(() => Hanger.values())
  static softBuild(value: EHanger): Hanger {
    return new Hanger(value);
  }

  static default(): EHanger {
    return EHanger.CASUAL;
  }

  static values(): EHanger[] {
    return Object.values(EHanger);
  }
}
