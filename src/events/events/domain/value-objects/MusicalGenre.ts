import {
  EMusicalGenres,
  Either,
  FvEnum,
  FvNumber,
  InvalidArgumentError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { EnumLogSoft } from '@/cross-cutting/domain/value-objects/EnumLogSoft';

export class MusicalGenre extends FvEnum<EMusicalGenres> {
  private constructor(value: EMusicalGenres) {
    super(value, MusicalGenre.values());
  }

  static build(value: EMusicalGenres): Either<InvalidArgumentError, MusicalGenre> {
    return this.values().includes(value)
      ? right(new MusicalGenre(value))
      : left(InvalidArgumentError.invalidValue({
        context: this.constructor.name,
        target: value,
      }));
  }

  @EnumLogSoft(() => MusicalGenre.values())
  static softBuild(value: EMusicalGenres): MusicalGenre {
    return new MusicalGenre(value);
  }

  static values(): EMusicalGenres[] {
    return Object.values(EMusicalGenres);
  }

  static random(): EMusicalGenres {
    const values = MusicalGenre.values();

    const randomIndex = FvNumber.random()
      .multiply(values.length)
      .floor()
      .toPrimitive();

    return values[randomIndex] as EMusicalGenres;
  }
}
