import {
  EEventServices,
  Either,
  FvEnum,
  InvalidArgumentError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { EnumLogSoft } from '@/cross-cutting/domain/value-objects/EnumLogSoft';

export class EventService extends FvEnum<EEventServices> {
  private constructor(value: EEventServices) {
    super(value, EventService.values());
  }

  static build(value: EEventServices): Either<InvalidArgumentError, EventService> {
    return this.values().includes(value)
      ? right(new EventService(value))
      : left(InvalidArgumentError.invalidValue({ context: this.constructor.name, target: value }));
  }

  @EnumLogSoft(() => EventService.values())
  static softBuild(value: EEventServices): EventService {
    return new EventService(value);
  }

  static values(): EEventServices[] {
    return Object.values(EEventServices);
  }

  isTicket(): boolean {
    return this.equalTo(EEventServices.TICKETS);
  }

  isGuestlist(): boolean {
    return this.equalTo(EEventServices.LIST);
  }

  isBooked(): boolean {
    return this.equalTo(EEventServices.BOOKED) || this.equalTo(EEventServices.BOOKINGS);
  }
}
