import {
  left,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';

import { type EventPreregisterUserEither } from '../domain/entities/EventPreregisterUser';
import { EventCriteriaMother } from '../domain/filters/EventCriteriaMother';

import type { IExternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/ExternalMessageBrokerClientContracts';
import type { InternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import type { RegisterPreregisterDto } from '@/events/events/domain/contracts/RegisterPreregisterDtoContract';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { EventRepository } from '../domain/contracts/EventRepository';

export class RegisterPreregisterUseCase implements UseCase<RegisterPreregisterDto, Promise<EventPreregisterUserEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly externalMessageBroker: IExternalMessageBrokerClient,
    private readonly internalMessageBroker: InternalMessageBrokerClient,
  ) { }

  @contextualizeError()
  async execute(dto: RegisterPreregisterDto): Promise<EventPreregisterUserEither> {
    const {
      eventId, fields, remarketing, language, newsletter,
    } = dto;

    const eventCriteria = EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(eventId.value), false);

    const eventResult = await this.eventRepository.find(eventCriteria);

    if (eventResult.isLeft()) {
      return left(NotFoundError.build({ context: this.constructor.name }).notAutoContextualizable());
    }

    const event = eventResult.value;

    const preregisterUserResult = event.createPreregisterUser({
      fields,
      isRemarketing: remarketing,
      preregisterId: event.getPreregisterId(),
      isNewsletter: newsletter,
    }, language);

    if (preregisterUserResult.isLeft()) {
      return left(preregisterUserResult.value);
    }

    const preregisterUser = preregisterUserResult.value;

    this.eventRepository.save(event);

    const domainEvents = event.pullDomainEvents();

    this.internalMessageBroker.publish(domainEvents);

    for (const _domainEvent of domainEvents) {
      this.externalMessageBroker.publish(_domainEvent);
    }

    return right(preregisterUser);
  }
}
