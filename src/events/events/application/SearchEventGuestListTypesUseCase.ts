import {
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { GuesListTypesSorting } from '@/guestLists/guestListsTypes/domain/services/GuesListTypesSorting';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { ServiceNotAvailableError } from '../domain/errors/ServiceNotAvailableError';

import type { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { IMicrositeEventChannelValidation } from '@/microsite/domain/contracts/MicrositeEventChannelValidationContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { EventRepository } from '../domain/contracts/EventRepository';
import type { IGetGuestListTypesAvailableService, SearchEventGuestListsEither } from '../domain/contracts/guestListType/EventGuestListContracts';
import type { SearchEventGuestListTypesDto } from '../domain/contracts/guestListType/SearchEventGuestListTypesContract';

export class SearchEventGuestListTypesUseCase implements UseCase<SearchEventGuestListTypesDto, Promise<SearchEventGuestListsEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly micrositeChannelService: IMicrositeChannelService,
    private readonly micrositeEventChannelValidation: IMicrositeEventChannelValidation,
    private readonly getGuestListTypesAvailableService: IGetGuestListTypesAvailableService,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventGuestListTypesDto): Promise<SearchEventGuestListsEither> {
    const {
      id, language, slug, organizationSlugs, smsSale, pagination,
    } = dto;

    const [eventResult, channelResult] = await Promise.all([
      this.eventRepository.find(EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(id), smsSale)),
      this.micrositeChannelService.execute({ slug, organizationSlugs }),
    ]);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    if (channelResult.isLeft()) {
      return left(channelResult.value);
    }

    const event = eventResult.value;
    const channel = channelResult.value;

    if (!event.hasGuestLists()) {
      return left(ServiceNotAvailableError.build({
        context: this.constructor.name,
        data: { dto },
      }).notAutoContextualizable());
    }

    const eventOrganizationId = UniqueEntityID.build(event.organizationId);
    const organizationCriteria = OrganizationCriteriaMother.idToMatch(eventOrganizationId);

    const [organizationResult] = await Promise.all([
      this.organizationRepository.find(organizationCriteria),
    ]);

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    const eventOrganization = organizationResult.value;

    const validation = await this.micrositeEventChannelValidation.execute({
      channel, eventOrganization, event,
    });

    if (validation.isLeft()) {
      return left(validation.value);
    }

    const guestListTypesResult = await this.getGuestListTypesAvailableService.execute({
      event, language, channel, pagination,
    });

    if (guestListTypesResult.isLeft()) {
      return left(guestListTypesResult.value);
    }

    const {
      prices, limits, pagination: paginationResult,
    } = guestListTypesResult.value;

    const guestListTypes = GuesListTypesSorting.execute(guestListTypesResult.value.guestListTypes);

    return right({
      event,
      guestListTypes,
      prices,
      limits,
      pagination: paginationResult,
    });
  }
}
