import {
  ECustomFeeApplyType,
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { OrganizationConfigurationCriteriaMother } from '@/organizations/organizationConfigurations/domain/filters/OrganizationConfigurationCriteriaMother';

import type { SearchFeesUseCase } from '@/fees/application/SearchFeesUseCase';
import type { ApplyFeesToTicketTypes } from '@/fees/domain/services/ApplyFeesToTicketTypes';
import type { OrganizationConfigurationRepository } from '@/organizations/organizationConfigurations/domain/contracts/OrganizationConfigurationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { SearchEventTicketsTypesEither } from '../domain/contracts/ticketType/EventTicketTypeTypes';
import type { SearchEventTicketTypesDto } from '../domain/contracts/ticketType/SearchEventTicketTypesContract';
import type { SearchEventTicketsTypesUseCase } from './SearchEventTicketsTypesUseCase';

export class SearchEventTicketTypesWithFeesUseCase implements UseCase<SearchEventTicketTypesDto, Promise<SearchEventTicketsTypesEither>> {
  constructor(
    private readonly searchEventTicketsTypesUseCase: SearchEventTicketsTypesUseCase,
    private readonly organizationConfigurationRepository: OrganizationConfigurationRepository,
    private readonly searchFeesUseCase: SearchFeesUseCase,
    private readonly applyFeesToTicketTypes: ApplyFeesToTicketTypes,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventTicketTypesDto): Promise<SearchEventTicketsTypesEither> {
    const searchEventTicketsTypesResult = await this.searchEventTicketsTypesUseCase.execute(dto);

    if (searchEventTicketsTypesResult.isLeft()) {
      return left(searchEventTicketsTypesResult.value);
    }

    const {
      event, ticketTypes, pagination,
    } = searchEventTicketsTypesResult.value;

    const organizationId = UniqueEntityID.build(event.organizationId);

    const organizationConfigurationCriteria = OrganizationConfigurationCriteriaMother.organizationToMatch(organizationId);

    const organizationConfigurationResult = await this.organizationConfigurationRepository.find(organizationConfigurationCriteria);

    const shouldShowFeesUpfront = organizationConfigurationResult.isRight()
      ? organizationConfigurationResult.value.areFeesShownUpfront
      : true;

    if (!shouldShowFeesUpfront) {
      return right({
        event,
        ticketTypes,
        pagination,
        language: dto.language,
      });
    }

    const searchFeesResult = await this.searchFeesUseCase.execute({
      applyTo: ECustomFeeApplyType.TICKETS,
      organizationId,
    });

    if (searchFeesResult.isLeft()) {
      return left(searchFeesResult.value);
    }

    const fees = searchFeesResult.value;

    if (fees.isEmpty()) {
      return right({
        event,
        ticketTypes,
        pagination,
        language: dto.language,
      });
    }

    const applyFeesResult = this.applyFeesToTicketTypes.execute({
      fees,
      ticketTypes,
    });

    if (applyFeesResult.isLeft()) {
      return left(applyFeesResult.value);
    }

    const ticketTypesWithFees = applyFeesResult.value;

    return right({
      event,
      ticketTypes: ticketTypesWithFees,
      pagination,
      language: dto.language,
    });
  }
}
