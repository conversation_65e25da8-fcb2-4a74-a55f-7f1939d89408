import {
  Maybe,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { TicketTypeCriteriaMother } from '@/tickets/ticketsTypes/domain/filters/TicketTypeCriteriaMother';
import { TicketTypesSorting } from '@/tickets/ticketsTypes/domain/services/TicketTypesSorting';
import { IGetTicketTypesExtrasService } from '@/tickets/ticketsTypes/domain/contracts/GetTicketTypesExtrasContracts';

import { ServiceNotAvailableError } from '../domain/errors/ServiceNotAvailableError';

import type { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { IMicrositeEventChannelValidation } from '@/microsite/domain/contracts/MicrositeEventChannelValidationContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { TicketTypeExtras } from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';
import type { TicketTypeRepository } from '@/tickets/ticketsTypes/domain/contracts/TicketTypeRepository';
import type { TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { EventRepository } from '../domain/contracts/EventRepository';
import type { SearchEventTicketsTypesEither } from '../domain/contracts/ticketType/EventTicketTypeTypes';
import type { SearchEventTicketTypesDto } from '../domain/contracts/ticketType/SearchEventTicketTypesContract';

export class SearchEventTicketsTypesUseCase implements UseCase<SearchEventTicketTypesDto, Promise<SearchEventTicketsTypesEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly micrositeChannelService: IMicrositeChannelService,
    private readonly micrositeEventChannelValidation: IMicrositeEventChannelValidation,
    private readonly ticketTypeRepository: TicketTypeRepository,
    private readonly getTicketTypesExtrasService: IGetTicketTypesExtrasService,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventTicketTypesDto): Promise<SearchEventTicketsTypesEither> {
    const {
      idOrCode, slug, organizationSlugs, smsSale, pagination, language,
    } = dto;

    const isUid = UniqueEntityID.isValid(idOrCode);

    const eventCriteria = isUid
      ? EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(idOrCode), smsSale)
      : EventCriteriaMother.findForMicrositeByCodeToMatch(idOrCode, smsSale);

    const [eventResult, channelResult] = await Promise.all([
      this.eventRepository.find(eventCriteria),
      this.micrositeChannelService.execute({ slug, organizationSlugs }),
    ]);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    if (channelResult.isLeft()) {
      return left(channelResult.value);
    }

    const event = eventResult.value;
    const channel = channelResult.value;

    if (!event.hasTickets()) {
      return left(ServiceNotAvailableError.build({
        context: this.constructor.name,
        data: { dto },
      }).notAutoContextualizable());
    }

    const eventId = UniqueEntityID.build(event.id);
    const organizationId = UniqueEntityID.build(event.organizationId);

    const ticketTypeCriteria = smsSale
      ? TicketTypeCriteriaMother.eventToMatch(eventId, pagination)
      : TicketTypeCriteriaMother.notSmsSaleToMatch(eventId, pagination);

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(organizationId);

    const [ticketsTypesResult, organizationResult] = await Promise.all([
      this.ticketTypeRepository.search(ticketTypeCriteria),
      this.organizationRepository.find(organizationCriteria),
    ]);

    if (ticketsTypesResult.isLeft()) {
      return left(ticketsTypesResult.value);
    }

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    let ticketTypes = ticketsTypesResult.value.ticketTypes;
    const paginationMetadata = ticketsTypesResult.value.pagination;
    const eventOrganization = organizationResult.value;

    const validation = await this.micrositeEventChannelValidation.execute({
      channel, eventOrganization, event,
    });

    if (validation.isLeft()) {
      return left(validation.value);
    }

    const ticketsTypesExtrasResult = await this.getTicketTypesExtrasService.execute({
      ticketTypes,
      channel,
      event,
      paylink: Maybe.none(),
    });

    if (ticketsTypesExtrasResult.isLeft()) {
      return left(ticketsTypesExtrasResult.value);
    }

    for (const ticketType of ticketTypes.values()) {
      ticketType.setAdditionalData(ticketsTypesExtrasResult.value.get(ticketType.id) as TicketTypeExtras);
    }

    if (!smsSale) {
      ticketTypes = this.filterSaleableTicketTypes(ticketTypes);
    }

    ticketTypes = TicketTypesSorting.execute(ticketTypes);

    return right({
      event,
      ticketTypes,
      pagination: paginationMetadata,
      language,
    });
  }

  private filterSaleableTicketTypes(ticketsTypes: TicketTypes): TicketTypes {
    const newTicketTypes = new Map(ticketsTypes);

    for (const ticketType of newTicketTypes.values()) {
      const ticketTypeId = ticketType.id;

      if (ticketType.additionalData.isEmpty()) {
        continue;
      }

      if (ticketType.additionalData.get().maximum === 0) {
        newTicketTypes.delete(ticketTypeId);
      }
    }

    return newTicketTypes;
  }
}
