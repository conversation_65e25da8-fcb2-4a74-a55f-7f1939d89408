import {
  ECustomFeeApplyType,
  EMicrositeChannel,
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { BillingAddressCriteriaMother } from '@/billingAddresses/domain/filters/BillingAddressCriteriaMother';
import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { LogSoftError } from '@/cross-cutting/domain/errors/LogSoftError';
import { isNotFoundError } from '@/cross-cutting/domain/helpers/guards';
import { ArtistCriteriaMother } from '@/events/artists/domain/filters/ArtistCriteriaMother';
import { EventConfigurationCriteriaMother } from '@/events/eventConfigurations/domain/filters/EventConfigurationCriteriaMother';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { LocationCriteriaMother } from '@/locations/domain/filters/LocationCriteriaMother';
import { Microsite } from '@/microsite/domain/entities/Microsite';
import { MicrositeCriteriaMother } from '@/microsite/domain/filters/MicrositeCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { EventEntity } from '../domain/entities/EventEntity';
import { EventImagesBuilder } from '../domain/entities/EventImagesBuilder';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type { ArtistRepository } from '@/events/artists/domain/contracts/ArtistRepository';
import type { Artist, ArtistsEither } from '@/events/artists/domain/entities/Artist';
import type { EventConfigurationRepository } from '@/events/eventConfigurations/domain/contracts/EventConfigurationRepository';
import type { EventConfigurationsEither } from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import type { FindEventDto } from '@/events/events/domain/contracts/FindEventContract';
import type { LocationFinder } from '@/locations/domain/services/LocationFinder';
import type { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { MicrositeRepository } from '@/microsite/domain/contracts/MicrositeRepository';
import type { OrganizationConfiguration } from '@/organizations/organizationConfigurations/domain/entities/OrganizationConfiguration';
import type { OrganizationConfigurationFinder } from '@/organizations/organizationConfigurations/domain/services/OrganizationConfigurationFinder';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventRepository } from '../domain/contracts/EventRepository';
import type {
  EventImages,
  FindEventEither,
  SetInternalEventConfigurations,
} from '../domain/entities/EventEntity';

export class FindEventUseCase implements UseCase<FindEventDto, Promise<FindEventEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly eventConfigurationRepository: EventConfigurationRepository,
    private readonly locationFinder: LocationFinder,
    private readonly artistRepository: ArtistRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly organizationConfigurationFinder: OrganizationConfigurationFinder,
    private readonly billingAddressRepository: BillingAddressRepository,
    private readonly micrositeRepository: MicrositeRepository,
    private readonly micrositeChannelService: IMicrositeChannelService,
  ) {}

  @contextualizeError()
  async execute(dto: FindEventDto): Promise<FindEventEither> {
    const {
      idOrCode, smsSale, slugChannel,
    } = dto;

    const isUid = UniqueEntityID.isValid(idOrCode);

    const eventCriteria = isUid
      ? EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(idOrCode), smsSale)
      : EventCriteriaMother.findForMicrositeByCodeToMatch(idOrCode, smsSale);

    const eventResult = await this.eventRepository.find(eventCriteria);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    const event = eventResult.value;
    const organizationMicrositeChannelId = await this.getMicrositeCriteria(event, slugChannel);
    const eventOrganizationId = UniqueEntityID.build(event.organizationId);

    const locationCriteria = LocationCriteriaMother.idToMatch(UniqueEntityID.build(event.locationId));
    const organizationCriteria = OrganizationCriteriaMother.idToMatch(eventOrganizationId);
    const billingAddressCriteria = BillingAddressCriteriaMother.organizationToMatch(eventOrganizationId);
    const eventConfigurationCriteria = EventConfigurationCriteriaMother.eventToMatch(UniqueEntityID.build(event.id));
    const micrositeCriteria = MicrositeCriteriaMother.organizationToMatch(UniqueEntityID.build(organizationMicrositeChannelId));

    const [
      location,
      organizationResult,
      billingAddressResult,
      eventConfigurationsResult,
      organizationConfiguration,
      micrositeResult,
      artistsResult,
    ] = await Promise.all([
      this.locationFinder.execute(locationCriteria),
      this.organizationRepository.find(organizationCriteria),
      this.billingAddressRepository.find(billingAddressCriteria),
      this.eventConfigurationRepository.search(eventConfigurationCriteria),
      this.organizationConfigurationFinder.execute({ organizationId: eventOrganizationId, feeApplyTo: ECustomFeeApplyType.TICKETS }),
      this.micrositeRepository.find(micrositeCriteria),
      this.searchArtists(event.artists),
    ]);

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    if (billingAddressResult.isLeft()) {
      if (isNotFoundError(billingAddressResult.value)) {
        this.logSoft(event.organizationId);

        return left(NotFoundError.build({ context: this.constructor.name, target: EventEntity.name }).notAutoContextualizable());
      }

      return left(billingAddressResult.value);
    }

    if (artistsResult.isLeft()) {
      return left(artistsResult.value);
    }

    const organization = organizationResult.value;
    const billingAddress = billingAddressResult.value;
    const artists = artistsResult.value;
    const images = this.makeImages(event, organization);

    if (location.isDefined()) {
      event.setLocation(location.get());
    }

    const micrositeServices = micrositeResult.isRight() ? micrositeResult.value.services : Microsite.defaultServices();
    const internalEventConfiguration = this.getConfigurations(eventConfigurationsResult, organizationConfiguration);

    event.sortServices(micrositeServices);
    event.setConfiguration(internalEventConfiguration);

    if (event.imagePlan.isEmpty() && organization.floorImage.isDefined()) {
      event.setImagePlan(organization.floorImage.get());
    }

    return right({
      event,
      artists,
      images,
      billingAddress,
      organization,
    });
  }

  private async searchArtists(spotifyIds: string[]): Promise<ArtistsEither> {
    if (spotifyIds.length === 0) {
      return right(new Map<IdPrimitive, Artist>());
    }

    const criteria = ArtistCriteriaMother.spotifyIdsToMatch(spotifyIds);

    return this.artistRepository.search(criteria);
  }

  private makeImages(event: EventEntity, organization: Organization): Maybe<EventImages> {
    const images = EventImagesBuilder().execute({ event, organization });

    return images.areEmpty()
      ? Maybe.none<EventImages>()
      : Maybe.some(images.getAll());
  }

  private getConfigurations(
    eventConfigurationsResult: EventConfigurationsEither,
    organizationConfiguration: Partial<OrganizationConfiguration>,
  ): SetInternalEventConfigurations {
    if (eventConfigurationsResult.isRight()) {
      return {
        eventConfigurations: eventConfigurationsResult.value,
        organizationConfiguration,
      };
    }

    return { organizationConfiguration };
  }

  private logSoft(organizationId: IdPrimitive): void {
    LogSoftError.billingAddressNotFound(organizationId);
  }

  private async getMicrositeCriteria(event: EventEntity, slugChannel: Maybe<string>): Promise<IdPrimitive> {
    if (slugChannel.isEmpty()) {
      return event.organizationId;
    }

    const micrositeChannelOrError = await this.micrositeChannelService.execute({
      slug: slugChannel.get(),
      organizationSlugs: new Set<string>(),
    });

    if (micrositeChannelOrError.isLeft()) {
      return event.organizationId;
    }

    const { type, channel } = micrositeChannelOrError.value;

    if (type === EMicrositeChannel.ORGANIZATION) {
      return (channel as Organization).id;
    }

    return event.organizationId;
  }
}
