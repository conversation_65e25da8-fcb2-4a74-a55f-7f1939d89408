import {
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { ServiceNotAvailableError } from '../domain/errors/ServiceNotAvailableError';

import type { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { IMicrositeEventChannelValidation } from '@/microsite/domain/contracts/MicrositeEventChannelValidationContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { SearchEventBookingZonesEither } from '../domain/contracts/bookingZones/EventBookingsZones';
import type { IGetBookingZonesServices } from '../domain/contracts/bookingZones/GetBookingZonesContracts';
import type { SearchEventBookingZonesDto } from '../domain/contracts/bookingZones/SearchEventBookingZonesContract';
import type { EventRepository } from '../domain/contracts/EventRepository';

export class SearchEventBookingZonesUseCase implements UseCase<SearchEventBookingZonesDto, Promise<SearchEventBookingZonesEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly micrositeChannelService: IMicrositeChannelService,
    private readonly micrositeEventChannelValidation: IMicrositeEventChannelValidation,
    private readonly getBookingZonesServices: IGetBookingZonesServices,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventBookingZonesDto): Promise<SearchEventBookingZonesEither> {
    const {
      id, slug, organizationSlugs, smsSale, pagination,
    } = dto;

    const [eventResult, channelResult] = await Promise.all([
      this.eventRepository.find(EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(id), smsSale)),
      this.micrositeChannelService.execute({ slug, organizationSlugs }),
    ]);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    if (channelResult.isLeft()) {
      return left(channelResult.value);
    }

    const event = eventResult.value;
    const channel = channelResult.value;

    if (!event.hasBookings()) {
      return left(ServiceNotAvailableError.build({
        context: this.constructor.name,
        data: { dto },
      }).notAutoContextualizable());
    }

    const eventOrganizationId = UniqueEntityID.build(event.organizationId);
    const organizationCriteria = OrganizationCriteriaMother.idToMatch(eventOrganizationId);

    const [organizationResult] = await Promise.all([
      this.organizationRepository.find(organizationCriteria),
    ]);

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    const eventOrganization = organizationResult.value;

    const validation = await this.micrositeEventChannelValidation.execute({
      channel, eventOrganization, event,
    });

    if (validation.isLeft()) {
      return left(validation.value);
    }

    const bookingsResult = await this.getBookingZonesServices.execute(event, pagination);

    if (bookingsResult.isLeft()) {
      return left(bookingsResult.value);
    }

    return right(bookingsResult.value);
  }
}
