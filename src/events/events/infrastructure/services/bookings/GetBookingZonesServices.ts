import {
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { BookingCriteriaMother } from '@/bookings/bookings/domain/filters/BookingCriteriaMother';
import { BookingTypeCollection } from '@/bookings/bookingTypes/domain/entities/BookingTypeCollection';
import { BookingTypeCriteriaMother } from '@/bookings/bookingTypes/domain/filters/BookingTypeCriteriaMother';
import { BookingZoneCriteriaMother } from '@/bookings/bookingZones/domain/filters/BookingZoneCriteriaMother';
import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { intersect } from '@/cross-cutting/domain/helpers/Intersect';

import type { BookingRepository } from '@/bookings/bookings/domain/contracts/BookingRepository';
import type { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import type { BookingTypeRepository } from '@/bookings/bookingTypes/domain/contracts/BookingTypeRepository';
import type { BookingZoneRepository } from '@/bookings/bookingZones/domain/contracts/BookingZoneRepository';
import type { BookingZone } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type {
  GetBookingZonesEither,
  IGetBookingZonesServices,
} from '@/events/events/domain/contracts/bookingZones/GetBookingZonesContracts';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { PaginationOption } from '@discocil/fv-criteria-converter-library/domain';

export class GetBookingZonesServices implements IGetBookingZonesServices {
  constructor(
    private readonly bookingZoneRepository: BookingZoneRepository,
    private readonly bookingTypeRepository: BookingTypeRepository,
    private readonly bookingRepository: BookingRepository,
  ) {}

  @contextualizeError()
  async execute(event: EventEntity, pagination?: PaginationOption): Promise<GetBookingZonesEither> {
    const eventId = UniqueEntityID.build(event.id);
    const organizationId = UniqueEntityID.build(event.organizationId);

    const bookingZoneCriteria = BookingZoneCriteriaMother.availablesToMatch(eventId, organizationId, pagination);
    const bookingTypeCriteria = BookingTypeCriteriaMother.availablesToMatch(eventId, organizationId);
    const bookingCriteria = BookingCriteriaMother.availablesToMatch(eventId, organizationId);

    const [bookingZonesResult, bookingTypesResult, bookingsResult] = await Promise.all([
      this.bookingZoneRepository.search(bookingZoneCriteria),
      this.bookingTypeRepository.search(bookingTypeCriteria),
      this.bookingRepository.search(bookingCriteria),
    ]);

    if (bookingZonesResult.isLeft()) {
      return left(bookingZonesResult.value);
    }

    if (bookingTypesResult.isLeft()) {
      return left(bookingTypesResult.value);
    }

    if (bookingsResult.isLeft()) {
      return left(bookingsResult.value);
    }

    const { bookingZones: zones, paginationMetadata } = bookingZonesResult.value;

    const types = [...bookingTypesResult.value.values()];
    const bookings = [...bookingsResult.value.values()];

    const typesByZones = new BookingTypeCollection();
    let occupiedSpaces = new Set<string>();

    zones.forEach((zone) => {
      const typesByZone = types.filter(type => zone.slugsTypes.includes(type.slug));

      typesByZones.add(zone.id, typesByZone);

      if (!zone.hasBookingTime() && zone.hasSpaces()) {
        occupiedSpaces = this.getOccupiedSpaces(zone, bookings);
      }

      const bookingsForMyZone = bookings.filter(booking => booking.zoneSlug.is(zone.slug));

      zone.addBookings(bookingsForMyZone);

      zone.spaces.forEach((space) => {
        const { id } = space;

        if (zone.thisSpaceIsBlocked(id)) {
          zone.blockSpace(id);
        }

        if (!zone.hasBookingTime() && occupiedSpaces.has(id)) {
          zone.setOccupiedSpace(id);
        }
      });
    });


    return right({
      event,
      zones,
      typesByZones,
      pagination: paginationMetadata,
    });
  }

  private getOccupiedSpaces(zone: BookingZone, bookings: Booking[]): Set<string> {
    const occupiedSpaces = new Set<string>();

    const spacesZoneWithCombinations = zone.getSpacesWithCombinations();
    const bookingsByZone = bookings.filter(booking => booking.zoneSlug.is(zone.slug));

    for (const booking of bookingsByZone) {
      if (booking.spaceId.isEmpty()) {
        continue;
      }

      const spaceId = booking.spaceId.get();

      occupiedSpaces.add(spaceId);

      const spaceCombination = spacesZoneWithCombinations.get(spaceId);

      if (!spaceCombination) {
        spacesZoneWithCombinations.forEach((space) => {
          if (space.combinationsIds.includes(spaceId)) {
            occupiedSpaces.add(space.id);
          }
        });

        continue;
      }

      spaceCombination.combinationsIds.forEach((combinationId) => {
        occupiedSpaces.add(combinationId);
      });

      for (const space of spacesZoneWithCombinations.values()) {
        if (booking.spaceId.isEmpty()) {
          continue;
        }

        const spaceZoneWithCombination = spacesZoneWithCombinations.get(booking.spaceId.get());

        if (!spaceZoneWithCombination) {
          continue;
        }

        const intersection = intersect(space.combinationsIds, spaceZoneWithCombination.combinationsIds);

        if (intersection.length > 0) {
          occupiedSpaces.add(space.id);
        }
      }
    }

    return occupiedSpaces;
  }
}
