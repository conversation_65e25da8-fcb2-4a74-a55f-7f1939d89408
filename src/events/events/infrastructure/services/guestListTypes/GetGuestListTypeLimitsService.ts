import {
  EM<PERSON>rositeChannel,
  FvN<PERSON>ber,
  Maybe,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { isNotFoundError } from '@/cross-cutting/domain/helpers/guards';
import { intersect } from '@/cross-cutting/domain/helpers/Intersect';
import { GuestListLimitCriteriaMother } from '@/guestLists/guestListLimits/domain/filters/GuestListLimitCriteriaMother';
import { GuestListOrganizationLimitCriteriaMother } from '@/guestLists/guestListOrganizationLimits/domain/filters/GuestListOrganizationLimitCriteriaMother';
import { GuestListCriteriaMother } from '@/guestLists/guestLists/domain/filters/GuestListCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  GuestListTypeLimitsEither,
  GuestListTypeLimitsRequest,
  IGetGuestListTypeLimitsService,
} from '@/events/events/domain/contracts/guestListType/GetGuestListTypeLimitsContracts';
import type { GuestListLimitRepository } from '@/guestLists/guestListLimits/domain/contracts/GuestListLimitRepository';
import type { GuestListOrganizationLimitRepository } from '@/guestLists/guestListOrganizationLimits/domain/contracts/GuestListOrganizationLimitRepository';
import type { GuestListOrganizationLimit } from '@/guestLists/guestListOrganizationLimits/domain/entities/GuestListOrganizationLimit';
import type { GuestListRepository } from '@/guestLists/guestLists/domain/contracts/GuestListRepository';
import type { GuestListType } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { User } from '@/user/domain/entities/User';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  InvalidArgumentError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

type LimitsResponse = {
  readonly maximum: number;
  readonly guestListOrganizationLimit?: GuestListOrganizationLimit | null;
  readonly organizationAssignedId: Maybe<UniqueEntityID>;
};

type LimitsEither = Either<MapperError | NotFoundError, LimitsResponse>;

type SalesEither = Either<
  InvalidArgumentError
  | InvalidFieldError
  | NotFoundError
  | MapperError
  | UnexpectedError,
  number
>;

export class GetGuestListTypeLimitsService implements IGetGuestListTypeLimitsService {
  constructor(
    private readonly guestListRepository: GuestListRepository,
    private readonly guestListLimitRepository: GuestListLimitRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly guestListOrganizationLimitRepository: GuestListOrganizationLimitRepository,
  ) {}

  @contextualizeError()
  async execute(dto: GuestListTypeLimitsRequest): Promise<GuestListTypeLimitsEither> {
    const limitsResult = await this.getLimits(dto);

    if (limitsResult.isLeft()) {
      return left(limitsResult.value);
    }

    const {
      maximum, guestListOrganizationLimit, organizationAssignedId,
    } = limitsResult.value;

    const salesResult = await this.getSales(
      dto,
      organizationAssignedId,
      guestListOrganizationLimit,
    );

    if (salesResult.isLeft()) {
      return left(salesResult.value);
    }

    const totalSales = salesResult.value;
    const maximumValue = maximum ?? 0;
    const salesAvailable = maximumValue - totalSales;

    return right({
      maximum: maximumValue,
      totalSales,
      available: salesAvailable > 0 ? salesAvailable : 0,
    });
  }

  private async getLimits(dto: GuestListTypeLimitsRequest): Promise<LimitsEither> {
    const { guestListType, channel } = dto;

    if (channel.type === EMicrositeChannel.REFERRER) {
      return this.micrositeChannelReferrer(guestListType, channel.channel as User);
    }

    const organization = channel.channel as Organization;

    if (!organization.id || organization.equalTo(guestListType.organizationId)) {
      return right({
        maximum: guestListType.clientLimit,
        organizationAssignedId: Maybe.none(),
      });
    }

    const criteria = GuestListLimitCriteriaMother.organizationAssignedLimitToMatch(
      UniqueEntityID.build(guestListType.eventId),
      guestListType.slug,
      UniqueEntityID.build(organization.id),
    );
    const limitResult = await this.guestListLimitRepository.find(criteria);

    if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
      return left(limitResult.value);
    }

    const maximum = limitResult.isRight() ? limitResult.value.maximum : 0;

    return right({
      maximum,
      organizationAssignedId: Maybe.none(),
    });
  }

  private async getSales(
    dto: GuestListTypeLimitsRequest,
    organizationAssignedId: Maybe<UniqueEntityID>,
    guestListOrganizationLimit?: GuestListOrganizationLimit | null,
  ): Promise<SalesEither> {
    const { guestListType, channel } = dto;

    if (guestListOrganizationLimit) {
      const criteria = GuestListCriteriaMother.groupSoldToMatch(
        UniqueEntityID.build(guestListType.eventId),
        UniqueEntityID.build(guestListType.id),
        UniqueEntityID.build(guestListType.organizationId),
        UniqueEntityID.build(guestListOrganizationLimit.organizationLimitedId),
      );

      const guestListResult = await this.guestListRepository.search(criteria);

      if (guestListResult.isLeft()) {
        return left(guestListResult.value);
      }

      return right([...guestListResult.value.values()].reduce((acc, guestList) => acc + guestList.signedUp, 0));
    }

    const guestListCriteria = this.getGuestListCriteria(channel, guestListType, organizationAssignedId);
    const totalsResult = await this.guestListRepository.getTotals(guestListCriteria);

    if (totalsResult.isLeft()) {
      return left(totalsResult.value);
    }

    return right(totalsResult.value.totalRegistered);
  }

  private async micrositeChannelReferrer(guestListType: GuestListType, user: User): Promise<LimitsEither> {
    let maximum = 0;

    let criteria = GuestListLimitCriteriaMother.specificLimitToMatch(
      UniqueEntityID.build(guestListType.eventId),
      guestListType.slug,
      UniqueEntityID.build(user.id),
    );

    let limitResult = await this.guestListLimitRepository.find(criteria);

    if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
      return left(limitResult.value);
    }

    if (limitResult.isRight()) {
      maximum = limitResult.value.maximum;
    }

    criteria = OrganizationCriteriaMother.hostGroupToMatch(guestListType.organizationId);
    const groupsResult = await this.organizationRepository.search(criteria);

    if (groupsResult.isLeft()) {
      return left(groupsResult.value);
    }

    const getGroupsIds = (groups: Organization[], userOrganizationsIds: string[]): UniqueEntityID[] => {
      const groupsIds = groups.map(item => item.id);

      return intersect(groupsIds, userOrganizationsIds).map(id => UniqueEntityID.build(id));
    };

    const groupsIds = getGroupsIds([...groupsResult.value.values()], user.organizations);

    if (limitResult.isLeft() && isNotFoundError(limitResult.value)) {
      limitResult = await this.guestListLimitRepository.find(
        GuestListLimitCriteriaMother.personalLimitToMatch(
          UniqueEntityID.build(guestListType.eventId),
          guestListType.slug,
          groupsIds,
        ),
      );

      if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
        return left(limitResult.value);
      }

      if (limitResult.isRight()) {
        maximum = limitResult.value.maximum;
      }
    }

    const ticketOrganizationLimitCriteria = GuestListOrganizationLimitCriteriaMother.groupLimitToMatch(
      UniqueEntityID.build(guestListType.id),
      UniqueEntityID.build(guestListType.eventId),
      groupsIds,
      UniqueEntityID.build(guestListType.organizationId),
    );

    const guestListOrganizationLimitResult = await this.guestListOrganizationLimitRepository.find(ticketOrganizationLimitCriteria);
    let guestListOrganizationLimit: GuestListOrganizationLimit | null = null;

    if (guestListOrganizationLimitResult.isLeft() && !isNotFoundError(guestListOrganizationLimitResult.value)) {
      return left(guestListOrganizationLimitResult.value);
    }

    if (guestListOrganizationLimitResult.isRight()) {
      guestListOrganizationLimit = guestListOrganizationLimitResult.value;

      if (limitResult.isRight()) {
        maximum = limitResult.value.maximum;
      }

      maximum = FvNumber.min(maximum, guestListOrganizationLimit.limit).toPrimitive();
    }

    if (limitResult.isRight()) {
      return right({
        maximum,
        guestListOrganizationLimit,
        organizationAssignedId: Maybe.none(),
      });
    }

    criteria = OrganizationCriteriaMother.hostNotGroupToMatch(
      user.organizations.map(item => UniqueEntityID.build(item)),
      guestListType.organizationId,
    );

    const organizationsAssignedResult = await this.organizationRepository.search(criteria);

    if (organizationsAssignedResult.isLeft()) {
      return left(organizationsAssignedResult.value);
    }

    const organizationsAssigned = [...organizationsAssignedResult.value.values()];
    const organizationAssignedIds = organizationsAssigned.map(org => UniqueEntityID.build(org.id));

    const guestListLimitCriteria = GuestListLimitCriteriaMother.personalLimitToMatch(
      UniqueEntityID.build(guestListType.eventId),
      guestListType.slug,
      organizationAssignedIds,
    );

    limitResult = await this.guestListLimitRepository.find(guestListLimitCriteria);

    if (limitResult.isLeft() && !isNotFoundError(limitResult.value)) {
      return left(limitResult.value);
    }

    let organizationAssignedId = Maybe.none<UniqueEntityID>();

    if (limitResult.isRight()) {
      maximum = limitResult.value.maximum;

      const limit = limitResult.value;

      organizationAssignedId = limit.organizationId.map(id => UniqueEntityID.build(id));
    }

    return right({
      maximum,
      guestListOrganizationLimit,
      organizationAssignedId,
    });
  }

  private getGuestListCriteria(channel: MicrositeChannel, guestListType: GuestListType, organizationAssignedId: Maybe<UniqueEntityID>): Criteria {
    if (channel.type === EMicrositeChannel.REFERRER) {
      const referrer = channel.channel as User;

      if (organizationAssignedId.isDefined()) {
        return GuestListCriteriaMother.organizationAssignedToMatch(
          organizationAssignedId.get(),
          UniqueEntityID.build(guestListType.eventId),
          UniqueEntityID.build(guestListType.id),
        );
      }

      return GuestListCriteriaMother.organizationAssignedAndReferrerToMatch(
        UniqueEntityID.build(guestListType.organizationId),
        UniqueEntityID.build(referrer.id),
        UniqueEntityID.build(guestListType.eventId),
        UniqueEntityID.build(guestListType.id),
      );
    }

    const organization = channel.channel as Organization;

    const isMicrositeChannelOrganization = organization.equalTo(guestListType.organizationId);

    return GuestListCriteriaMother.organizationAssignedToMatch(
      UniqueEntityID.build(organization.id),
      UniqueEntityID.build(guestListType.eventId),
      UniqueEntityID.build(guestListType.id),
      isMicrositeChannelOrganization,
    );
  }
}
