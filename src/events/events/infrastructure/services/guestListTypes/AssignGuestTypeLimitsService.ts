import {
  FvN<PERSON>ber,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { GuestListLimit } from '@/guestLists/guestListLimits/domain/entities/GuestListLimit';
import { GuestListCriteriaMother } from '@/guestLists/guestLists/domain/filters/GuestListCriteriaMother';

import type {
  AssignGuestTypeLimitsEither,
  AssignGuestTypeLimitsRequest,
  IAssignGuestTypeLimitsService,
} from '@/events/events/domain/contracts/guestListType/AssignGuestListTypeLimitsContracts';
import type { IGetGuestListTypeLimitsService } from '@/events/events/domain/contracts/guestListType/GetGuestListTypeLimitsContracts';
import type { GuestListRepository } from '@/guestLists/guestLists/domain/contracts/GuestListRepository';

export class AssignGuestTypeLimitsService implements IAssignGuestTypeLimitsService {
  constructor(
    private readonly guestListRepository: GuestListRepository,
    private readonly getGuestListTypeLimits: IGetGuestListTypeLimitsService,
  ) {}

  @contextualizeError()
  async execute(dto: AssignGuestTypeLimitsRequest): Promise<AssignGuestTypeLimitsEither> {
    const { guestListType } = dto;

    const criteria = GuestListCriteriaMother.numberOfTotalRegistered(
      UniqueEntityID.build(guestListType.id),
      UniqueEntityID.build(guestListType.eventId),
    );

    const totalSoldResult = await this.guestListRepository.getTotals(criteria);

    if (totalSoldResult.isLeft()) {
      return left(totalSoldResult.value);
    }

    const { totalRegistered } = totalSoldResult.value;

    let maximumAvailable = guestListType.maxRate;
    const availableRate = FvNumber.build(guestListType.maxRate).subtract(totalRegistered).toPrimitive();
    let availableAllowed = availableRate;

    const maximumsAllowed = new Set<number>();

    maximumsAllowed.add(GuestListLimit.maxSimultaneous);
    maximumsAllowed.add(guestListType.maxCustomers);

    if (guestListType.isLimited()) {
      const limitResult = await this.getGuestListTypeLimits.execute(dto);

      if (limitResult.isLeft()) {
        return left(limitResult.value);
      }

      const limit = limitResult.value;

      availableAllowed = limit.available;
      maximumAvailable = limit.maximum;

      maximumsAllowed.add(availableRate);
    }

    maximumsAllowed.add(availableAllowed);

    const maximum = FvNumber.min(...maximumsAllowed);

    return right({
      maximum: maximum.toPrimitive(),
      originalMaximum: maximumAvailable,
      available: availableAllowed,
    });
  }
}
