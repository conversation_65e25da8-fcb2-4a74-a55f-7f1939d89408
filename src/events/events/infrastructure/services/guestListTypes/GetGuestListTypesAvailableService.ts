import {
  FvN<PERSON>ber,
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { Translator } from '@/cross-cutting/domain/contracts/TranslatorContracts';
import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { GuestListTypeCriteriaMother } from '@/guestLists/guestListsTypes/domain/filters/GuestListTypeCriteriaMother';

import type {
  AssignGuestTypeLimits,
  IAssignGuestTypeLimitsService,
} from '@/events/events/domain/contracts/guestListType/AssignGuestListTypeLimitsContracts';
import type {
  GuestListTypesAvailableEither,
  GuestListTypesAvailableRequest,
  GuestListTypesLimits,
  GuestListTypeTotalPrice,
  GuestListTypeTotalPrices,
  IGetGuestListTypesAvailableService,
} from '@/events/events/domain/contracts/guestListType/EventGuestListContracts';
import type { GuestListTypeRepository } from '@/guestLists/guestListsTypes/domain/contracts/GuestListTypeRepository';
import type { GuestListType, GuestListTypes } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class GetGuestListTypesAvailableService implements IGetGuestListTypesAvailableService {
  constructor(
    private readonly guestListTypeRepository: GuestListTypeRepository,
    private readonly assignGuestTypeLimitsService: IAssignGuestTypeLimitsService,
    private readonly localizationService: Translator,
  ) {}

  @contextualizeError()
  async execute(dto: GuestListTypesAvailableRequest): Promise<GuestListTypesAvailableEither> {
    const {
      event, channel, pagination,
    } = dto;

    const guestListTypeCriteria = GuestListTypeCriteriaMother.activesInEventToMatch(
      UniqueEntityID.build(event.id),
      pagination,
    );

    const guestListTypesResult = await this.guestListTypeRepository.search(guestListTypeCriteria);

    if (guestListTypesResult.isLeft()) {
      return left(guestListTypesResult.value);
    }

    const { guestListTypes, paginationMetadata } = guestListTypesResult.value;
    const guestListTypesPrices: GuestListTypeTotalPrices = new Map<IdPrimitive, GuestListTypeTotalPrice>();
    const responseGuestListTypes: GuestListTypes = new Map<IdPrimitive, GuestListType>();
    const guestListTypesLimits: GuestListTypesLimits = new Map<IdPrimitive, AssignGuestTypeLimits>();

    for (const guestListType of guestListTypes.values()) {
      const guestListTypeLimitsResult = await this.assignGuestTypeLimitsService.execute({ guestListType, channel });

      if (guestListTypeLimitsResult.isLeft()) {
        return left(guestListTypeLimitsResult.value);
      }

      const prices = guestListType.options.reduce(
        (acc: GuestListTypeTotalPrice, option) => {
          const price = option.price;

          return {
            min: FvNumber.min(price, acc.min).toPrimitive(),
            max: FvNumber.max(price, acc.max).toPrimitive(),
          };
        },
        { min: 0, max: 0 },
      );

      guestListTypesPrices.set(guestListType.id, prices);

      guestListType.setOptionsSummaries(event, this.localizationService);

      const guestListTypeLimits = guestListTypeLimitsResult.value;

      guestListTypesLimits.set(guestListType.id, guestListTypeLimits);

      if (guestListTypeLimits.originalMaximum > 0 || guestListType.isPublic()) {
        responseGuestListTypes.set(guestListType.id, guestListType);
      }
    }

    return right({
      guestListTypes: responseGuestListTypes,
      prices: guestListTypesPrices,
      limits: guestListTypesLimits,
      pagination: paginationMetadata,
    });
  }
}
