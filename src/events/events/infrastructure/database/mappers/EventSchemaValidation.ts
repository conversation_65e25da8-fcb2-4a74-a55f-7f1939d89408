import type { JSONSchemaType } from 'ajv';
import type { EventSchemaOriginalType } from '../schemas/EventSchemaType';

export const eventValidationSchema: JSONSchemaType<EventSchemaOriginalType> = {
  title: 'Event Json Schema Validation',
  required: ['negocio_id', 'ubicacion_id', 'fecha', 'inicio', 'fin'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    inicio: { type: 'number' },
    fin: { type: 'number' },
    negocio_nombre: { type: 'string' },
    ubicacion_id: { type: 'string' },
    tz_name: { type: 'string' },
    provincia: { type: 'string' },
    provincia_slug: { type: 'string' },
    municipio: { type: 'string' },
    weather: { type: 'object', nullable: true },
    serie: { type: 'string' },
    codigo: { type: 'string' },
    fecha: { type: 'number' },
    dia: { type: 'number' },
    nombre: { type: 'string' },
    slug: { type: 'string' },
    descripcion: { type: 'string' },
    edad: { type: 'number' },
    autorizacion_paterna: {
      type: 'string',
      nullable: true,
    },
    activo: { type: 'boolean' },
    visible: { type: 'boolean' },
    is_private: { type: 'boolean' },
    cancelado: { type: 'number' },
    plan: { type: 'number' },
    sms_sent: { type: 'number' },
    currency: { type: 'string' },
    funciones: {
      type: 'array',
      items: { type: 'string' },
    },
    servicios: {
      type: 'array',
      items: { type: 'string' },
    },
    percha: { type: 'string' },
    ambiente: {
      type: 'array',
      items: { type: 'string' },
    },
    generos: {
      type: 'array',
      items: { type: 'string' },
    },
    artistas: {
      type: 'array',
      items: { type: 'string' },
    },
    variable_discocil: { type: 'number' },
    sms_price: { type: 'number' },
    cambio_nombre_price: { type: 'number' },
    aplicar_comision_evento: { type: 'boolean' },
    procesando: { type: 'boolean' },
    procesado: { type: 'boolean' },
    fotos_num: { type: 'number' },
    demo: { type: 'boolean' },
    imagen: { type: 'string' },
    negocio_imagen: { type: 'string' },
    images: {
      type: 'object',
      required: [],
      properties: {
        med: {
          type: 'object',
          required: [],
          nullable: true,
          properties: {
            original: { type: 'string' },
            versioned: { type: 'string' },
            sized: { type: 'string' },
            isDefault: { type: 'boolean' },
          },
        },
        min: {
          type: 'object',
          required: [],
          nullable: true,
          properties: {
            original: { type: 'string' },
            versioned: { type: 'string' },
            sized: { type: 'string' },
            isDefault: { type: 'boolean' },
          },
        },
        sm: {
          type: 'object',
          required: [],
          nullable: true,
          properties: {
            original: { type: 'string' },
            versioned: { type: 'string' },
            sized: { type: 'string' },
            isDefault: { type: 'boolean' },
          },
        },
      },
    },
    ggdd: { type: 'object' },
    imagen_plano: { type: 'string' },
    show_imagen_plano: { type: 'boolean' },
    fecha_limite_venta: { type: 'number', nullable: true },
    qr_menu_id: { type: 'string' },
    show_location: { type: 'boolean' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
