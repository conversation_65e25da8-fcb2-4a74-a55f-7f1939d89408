import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { EventPreregisterUser, type EventPreregisterUserEither } from '@/events/events/domain/entities/EventPreregisterUser';

import { eventPreregisterUserValidationSchema } from './EventPreregisterUserSchemaValidation';

import type Ajv from 'ajv';
import type { EventPreregisterUserSchemaType } from '../schemas/EventPreregisterUserSchemaType';

export class EventPreregisterUserMapper {
  static execute(data: EventPreregisterUserSchemaType): EventPreregisterUserEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(eventPreregisterUserValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: EventPreregisterUserMapper.name,
        data,
        target: validate.errors,
      });
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: EventPreregisterUserSchemaType): EventPreregisterUserEither {
    return EventPreregisterUser.build({
      id: data._id,
      preregisterId: data.preregister_id,
      isRemarketing: data.is_remarketing,
      fields: data.fields,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  };
}
