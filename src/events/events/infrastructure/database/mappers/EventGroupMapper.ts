import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { EventGroup } from '@/events/events/domain/entities/EventGroup';

import { eventGroupSchemaValidation } from './EventGroupSchemaValidation';

import type { EventGroupEither } from '@/events/events/domain/entities/EventGroup';
import type { EventGroupSchemaType } from '@/events/events/infrastructure/database/schemas/EventGroupSchemaType';
import type Ajv from 'ajv';

export class EventGroupMapper {
  static execute(data: EventGroupSchemaType): EventGroupEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(eventGroupSchemaValidation);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: EventGroupMapper.name,
        data,
        target: validate.errors,
      });
    }

    return EventGroup.build({
      id: data._id,
      organizationId: data.negocio_id,
      code: data.code,
      eventIds: new Set(data.event_ids ?? []),
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}
