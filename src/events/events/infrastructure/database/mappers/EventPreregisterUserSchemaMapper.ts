import type { EventPreregisterUser } from '@/events/events/domain/entities/EventPreregisterUser';
import type { EventPreregisterUserSchemaType } from '../schemas/EventPreregisterUserSchemaType';

export class EventPreregisterUserSchemaMapper {
  static execute(entity: EventPreregisterUser): Partial<EventPreregisterUserSchemaType> {
    return {
      _id: entity.id,
      preregister_id: entity.preregisterId,
      is_remarketing: entity.isRemarketing,
      fields: entity.fields,
      created_at: entity.createdAt,
      created_by: entity.createdBy,
      updated_at: entity.updatedAt,
      updated_by: entity.updatedBy,
      removed_at: entity.removedAt,
      removed_by: entity.removedBy,
    };
  }
}
