import type { JSONSchemaType } from 'ajv';
import type { EventPreregisterUserSchemaType } from '../schemas/EventPreregisterUserSchemaType';

export const eventPreregisterUserValidationSchema: JSONSchemaType<EventPreregisterUserSchemaType> = {
  title: 'Event Json Schema Validation',
  required: ['preregister_id', 'fields'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    preregister_id: { type: 'string' },
    is_remarketing: { type: 'boolean' },
    fields: { type: 'object' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
