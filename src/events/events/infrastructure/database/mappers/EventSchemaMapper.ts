import { FvDate } from '@discocil/fv-domain-library/domain';

import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { EventSchemaOriginalType } from '../schemas/EventSchemaType';

export class EventSchemaMapper {
  static execute(event: EventEntity): Partial<EventSchemaOriginalType> {
    return {
      _id: event.id,
      negocio_id: event.organizationId,
      nombre: event.name,
      descripcion: event.description.fold(() => '', item => item),
      slug: event.slug,
      codigo: event.code,
      currency: event.currency,
      activo: event.isActive,
      cancelado: event.canceled.fold(
        () => 0,
        date => FvDate.create(date).toSeconds(),
      ),
      plan: event.plan,
      sms_sent: event.smsSent,
      sms_price: event.smsPrice,
      cambio_nombre_price: event.changeNamePrice,
      aplicar_comision_evento: event.applyCommissionEvent,
      visible: event.isVisible,
      demo: event.demo,
      ambiente: event.atmosphere,
      generos: event.musicalGenres,
      percha: event.perch,
      artistas: event.artists,
      variable_discocil: event.variableDiscocil,
      fecha: event.getDateInSeconds(),
      dia: event.day,
      edad: event.age,
      fecha_limite_venta: event.getDateLimitSaleInSeconds(),
      fotos_num: event.photosNum,
      funciones: event.functions,
      imagen: event.image.fold(() => undefined, item => item),
      imagen_plano: event.imagePlan.fold(() => '', item => item),
      municipio: event.municipality,
      negocio_imagen: event.businessImage,
      negocio_nombre: event.businessName,
      provincia: event.province.fold(() => undefined, item => item),
      provincia_slug: event.provinceSlug.fold(() => '', item => item),
      servicios: event.services,
      qr_menu_id: event.qrMenuId.fold(() => '', item => item),
      show_imagen_plano: event.showImagePlan,
      show_location: event.showLocation,
      weather: null,
      ggdd: {},
      images: event.images.fold(() => undefined, item => item),
      procesando: event.processing,
      procesado: event.processed,
      ubicacion_id: event.locationId,
      tz_name: event.tzName.fold(() => undefined, item => item),
      serie: '',
      autorizacion_paterna: event.parentalAuthorization.fold(() => undefined, item => item),
      inicio: FvDate.create(event.startDate).toSeconds(),
      fin: FvDate.create(event.endDate).toSeconds(),
      created_at: event.createdAt,
      created_by: event.createdBy,
      updated_at: event.updatedAt,
      updated_by: event.updatedBy,
      removed_at: event.removedAt,
      removed_by: event.removedBy,
    };
  }
}
