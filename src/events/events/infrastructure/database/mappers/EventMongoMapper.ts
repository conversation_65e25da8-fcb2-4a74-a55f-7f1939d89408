import {
  <PERSON>urrency,
  FvDate,
  FvNumber,
  Maybe,
} from '@discocil/fv-domain-library/domain';

import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { Hanger } from '@/events/events/domain/value-objects/Hanger';
import { ImageMapper } from '@/organizations/organizations/infrastructure/database/mappers/ImageMapper';
import { User } from '@/user/domain/entities/User';

import type { Images } from '@/cross-cutting/domain/contracts/Images';
import type { EventEither } from '@/events/events/domain/entities/EventEntity';
import type { EventPreregisterPrimitives } from '@/events/events/domain/entities/EventPreregister';
import type { EventSchemaType } from '../schemas/EventSchemaType';

export class EventMongoMapper {
  static execute(data: EventSchemaType): EventEither {
    const createFromSeconds = (value: number): FvDate => FvDate.createFromSeconds(value);

    const startDate = createFromSeconds(data.inicio);
    const endDate = createFromSeconds(data.fin);
    const date = createFromSeconds(data.fecha);

    const dateLimitSale = FvNumber.is(data.fecha_limite_venta)
      ? createFromSeconds(data.fecha_limite_venta)
      : date;

    const dateCanceled = data.cancelado !== 0
      ? Maybe.some(createFromSeconds(data.cancelado).toPrimitive())
      : Maybe.none<Date>();

    let preregister = Maybe.none<EventPreregisterPrimitives>();

    if ('preregister' in data && data.preregister !== null) {
      let endDate = Maybe.none<Date>();

      if (data.preregister.endDate !== 0) {
        endDate = Maybe.some(
          FvDate.createFromSeconds(data.preregister.endDate).toPrimitive(),
        );
      }

      preregister = Maybe.some({
        id: data.preregister._id,
        isActive: data.preregister.is_active,
        endDate,
      });
    }

    const imageHasSomeSizes = Boolean(
      data.images
      && (data.images.med || data.images.min || data.images.sm),
    );

    const builtImages = imageHasSomeSizes
      ? ImageMapper.execute(data.images ?? {})
      : Maybe.none<Images>();

    return EventEntity.build({
      id: data._id,
      organizationId: data.negocio_id,
      atmosphere: data.ambiente ?? [],
      applyCommissionEvent: data.aplicar_comision_evento ?? false,
      artists: data.artistas ?? [],
      canceled: dateCanceled,
      code: data.codigo ?? null,
      currency: data.currency ?? ECurrency.EUR,
      demo: data.demo ?? false,
      description: Maybe.fromValue(data.descripcion),
      day: data.dia ?? null,
      age: data.edad ?? User.MINIMUM_REQUIRED_AGE,
      date: date.toPrimitive(),
      dateLimitSale: dateLimitSale.toPrimitive(),
      photosNum: data.fotos_num ?? 0,
      functions: data.funciones ?? [],
      musicalGenres: data.generos ?? [],
      image: Maybe.fromValue(data.imagen),
      images: builtImages,
      imagePlan: Maybe.fromValue(data.imagen_plano),
      municipality: data.municipio ?? null,
      businessImage: data.negocio_imagen ?? null,
      businessName: data.negocio_nombre ?? null,
      name: data.nombre ?? null,
      perch: data.percha ?? Hanger.default(),
      plan: data.plan ?? 0,
      processed: data.procesado ?? true,
      processing: data.procesando ?? false,
      province: Maybe.fromValue(data.provincia),
      provinceSlug: Maybe.fromValue(data.provincia_slug),
      qrMenuId: Maybe.fromValue(data.qr_menu_id),
      services: data.servicios ?? [],
      showImagePlan: data.show_imagen_plano,
      showLocation: data.show_location ?? true,
      slug: data.slug ?? null,
      smsPrice: data.sms_price ?? 0,
      smsSent: data.sms_sent ?? 0,
      tzName: Maybe.fromValue(data.tz_name),
      locationId: data.ubicacion_id,
      variableDiscocil: data.variable_discocil ?? 0,
      isVisible: data.visible ?? true,
      isPrivate: data.is_private ?? false,
      startDate: startDate.toPrimitive(),
      endDate: endDate.toPrimitive(),
      isActive: data.activo ?? true,
      changeNamePrice: data.cambio_nombre_price ?? 0,
      parentalAuthorization: Maybe.fromValue(data.autorizacion_paterna),
      preregister,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}
