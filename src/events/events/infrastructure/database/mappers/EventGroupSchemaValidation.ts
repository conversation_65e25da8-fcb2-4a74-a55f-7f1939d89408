import type { JSONSchemaType } from 'ajv';
import type { EventGroupSchemaType } from '../schemas/EventGroupSchemaType';

export const eventGroupSchemaValidation: JSONSchemaType<EventGroupSchemaType> = {
  title: 'EventGroup JSON Schema',
  required: ['_id'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    event_ids: { type: 'array', items: { type: 'string' } },
    code: { type: 'string' },
    name: { type: 'string' },
    description: { type: 'string' },
    color: { type: 'string' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
