import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { EventPreregisterUserSchemaMapper } from '../mappers/EventPreregisterUserSchemaMapper';
import { eventPreregisterUserType } from '../schemas/EventPreregisterUserType';

import type { EventPreregisterUserRepository } from '@/events/events/domain/contracts/EventPreregisterUserRepository';
import type { EventPreregisterUser, EventPreregisterUserKeys } from '@/events/events/domain/entities/EventPreregisterUser';
import type { EventPreregisterUserSchemaType } from './../schemas/EventPreregisterUserSchemaType';

export type PropertiesMapper = Partial<Record<EventPreregisterUserKeys, keyof EventPreregisterUserSchemaType>>;

export class EventPreregisterUserMongoRepository extends MongoRepository implements EventPreregisterUserRepository {
  protected getSchema(): Schema {
    return new Schema(eventPreregisterUserType);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'preregister_users';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      preregisterId: 'preregister_id',
    };
  }

  async save(entity: EventPreregisterUser): Promise<void> {
    const toSave = EventPreregisterUserSchemaMapper.execute(entity);

    const filter = { _id: entity.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }
}
