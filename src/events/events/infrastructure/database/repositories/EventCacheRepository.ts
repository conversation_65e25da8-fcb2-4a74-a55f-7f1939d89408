import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { PaginationMetadataMapper } from '@/cross-cutting/infrastructure/services/paginationMetadataMapper';
import { EventJsonMapper } from '@/events/events/domain/mappers/EventJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { PaginationMetadataPrimitives } from '@/cross-cutting/infrastructure/services/paginationMetadataMapper';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type {
  EventEither,
  EventEntity,
  Events,
  EventsEither,
} from '@/events/events/domain/entities/EventEntity';
import type { EventJsonPrimitives } from '@/events/events/domain/mappers/EventJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class EventCacheRepository extends CacheRepository implements EventRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: EventRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<EventEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: EventJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = EventJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        EventJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async save(event: EventEntity): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(event);
  }

  async search(criteria: Criteria): Promise<EventsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: { events: EventJsonPrimitives[]; pagination: PaginationMetadataPrimitives; } = JSON.parse(cacheHit);
      const events: Events = new Map<IdPrimitive, EventEntity>();

      for (const _primitive of jsonPrimitives.events) {
        const entityOrError = EventJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        events.set(_primitive.id, entityOrError.value);
      }

      return right({
        events,
        pagination: PaginationMetadataMapper.fromPrimitives(jsonPrimitives.pagination),
      });
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const { events, pagination } = repositoryResult.value;
      const eventLength = events.size;
      let eventCacheIndex = 0;
      const jsonEvents = new Array<EventJsonPrimitives>(eventLength);

      if (eventLength > 0) {
        for (const _event of events.values()) {
          jsonEvents[eventCacheIndex] = EventJsonMapper.toJson(_event);

          eventCacheIndex++;
        }
      }

      const resultForCache = {
        events: jsonEvents,
        pagination: PaginationMetadataMapper.toPrimitives(pagination),
      };

      await this.cacheHandler.set(cacheKey, resultForCache, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
