import { Filter, Paginator } from '@discocil/fv-criteria-converter-library/domain';
import {
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { TokenFilter } from '@/events/events/domain/filters/bookingSpaceAvailability/TokenFilter';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { eventSchema } from '@/events/events/infrastructure/database/schemas/EventSchema';

import { EventMongoMapper } from '../mappers/EventMongoMapper';

import { BookingSpaceAvailabilityMongoRepository } from './BookingSpaceAvailabilityMongoRepository';
import { EventPreregisterUserMongoRepository } from './EventPreregisterUserMongoRepository';

import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { BookingSpaceAvailability } from '@/events/events/domain/entities/BookingSpaceAvailability';
import type {
  EventEither,
  EventKeys,
  Events,
  EventsEither,
} from '@/events/events/domain/entities/EventEntity';
import type { Criteria, RequiredCriteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventSchemaOriginalType, EventSchemaType } from '../schemas/EventSchemaType';

type PropertiesMapper = Partial<Record<EventKeys, keyof EventSchemaOriginalType>>;

export class EventMongoRepository extends MongoRepository implements EventRepository {
  private readonly eventPreregisterUserRepository = new EventPreregisterUserMongoRepository(this.connection);
  private readonly bookingSpaceAvailabilityMongoRepository = new BookingSpaceAvailabilityMongoRepository(this.connection);

  protected getSchema(): Schema {
    return new Schema(eventSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'eventos';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      isVisible: 'visible',
      date: 'fecha',
      startDate: 'inicio',
      endDate: 'fin',
      code: 'codigo',
      isActive: 'activo',
      removedAt: 'removed_at',
      isPrivate: 'is_private',
    };
  }

  async find(criteria: Criteria): Promise<EventEither> {
    const checkBookingSpacesAvailability = (criteria: Criteria): boolean => {
      const filters = criteria.filters?.filters;
      const firtsFilter = filters?.[0];

      return filters?.length === 1
        && firtsFilter instanceof Filter
        && firtsFilter.field.value === TokenFilter.field;
    };

    const isBookingSpacesAvailability = checkBookingSpacesAvailability(criteria);
    let bookingSpaceAvailability = Maybe.none<BookingSpaceAvailability>();

    if (isBookingSpacesAvailability) {
      const bookingSpaceAvailabilityOrError = await this.bookingSpaceAvailabilityMongoRepository.find(criteria);

      if (bookingSpaceAvailabilityOrError.isLeft()) {
        return left(bookingSpaceAvailabilityOrError.value);
      }

      bookingSpaceAvailability = Maybe.some(bookingSpaceAvailabilityOrError.value);

      criteria = EventCriteriaMother.idToMatch(UniqueEntityID.build(bookingSpaceAvailability.get().eventId));
    }

    const queryResponse = (await this.customQueryFinder<EventSchemaType[]>(criteria)).shift();

    if (queryResponse) {
      const eventOrError = EventMongoMapper.execute(queryResponse);

      if (eventOrError.isLeft()) {
        return left(eventOrError.value);
      }

      if (bookingSpaceAvailability.isDefined()) {
        eventOrError.value.setBookingSpaceAvailability(bookingSpaceAvailability.get());
      }

      return eventOrError;
    }

    return left(NotFoundError.build({ context: this.constructor.name, target: EventEntity.name }));
  }

  async save(event: EventEntity): Promise<void> {
    if (!event.hasDirtyPreregisterUsers()) {
      return;
    }

    const users = event.getDirtyPreregisterUsers();

    const promises: Promise<void>[] = [];

    for (const user of users.values()) {
      promises.push(this.eventPreregisterUserRepository.save(user));
    }

    await Promise.all(promises);
  }

  async search(criteria: Criteria): Promise<EventsEither> {
    const response: Events = new Map<IdPrimitive, EventEntity>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<EventSchemaType[]>();

    if (queryResponse.length === 0) {
      return right({ events: response });
    }

    for (const model of queryResponse) {
      const eventResult = EventMongoMapper.execute(model);

      if (eventResult.isLeft()) {
        return left(eventResult.value);
      }

      const event = eventResult.value;

      response.set(event.id, event);
    }

    if (criteria.pagination) {
      const total = await connection.countDocuments(filterQuery.filter);
      const requiredCriteria = criteria as RequiredCriteria;

      return right({
        events: response,
        pagination: Paginator.execute(total, requiredCriteria, queryResponse.length),
      });
    }

    return right({ events: response });
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'preregister',
          let: { id: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$id', '$event_id'] } } },
            {
              $project: {
                _id: 1,
                is_active: 1,
                endDate: 1,
              },
            },
          ],
          as: 'preregister',
        },
      },
      { $set: { preregister: { $arrayElemAt: ['$preregister', 0] } } },
    ]) as unknown as T;
  }
}
