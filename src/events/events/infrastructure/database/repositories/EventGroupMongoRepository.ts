import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EventGroup } from '@/events/events/domain/entities/EventGroup';

import { EventGroupMapper } from '../mappers/EventGroupMapper';
import { eventGroupSchema } from '../schemas/EventGroupSchema';

import type { EventGroupRepository } from '@/events/events/domain/contracts/eventGroup/EventGroupRepository';
import type {
  EventGroupEither,
  EventGroupKeys,
  EventGroupsEither,
} from '@/events/events/domain/entities/EventGroup';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventGroupSchemaType } from '../schemas/EventGroupSchemaType';

type PropertiesMapper = Partial<Record<EventGroupKeys, keyof EventGroupSchemaType>>;

export class EventGroupMongoRepository extends MongoRepository implements EventGroupRepository {
  protected getSchema(): Schema {
    return new Schema(eventGroupSchema);
  }

  protected getModel(): string {
    return 'event_calendars';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      code: 'code',
      removedAt: 'removed_at',
      organizationId: 'negocio_id',
      eventIds: 'event_ids',
    };
  }

  async find(criteria: Criteria): Promise<EventGroupEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<EventGroupSchemaType>();

    return queryResponse
      ? EventGroupMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: EventGroup.name }));
  }

  async search(criteria: Criteria): Promise<EventGroupsEither> {
    const response = new Map<IdPrimitive, EventGroup>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<EventGroupSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const eventGroupResult = EventGroupMapper.execute(model);

      if (eventGroupResult.isLeft()) {
        return left(eventGroupResult.value);
      }

      const eventGroup = eventGroupResult.value;

      response.set(eventGroup.id, eventGroup);
    }

    return right(response);
  }
}
