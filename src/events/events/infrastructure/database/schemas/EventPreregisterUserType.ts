
export const eventPreregisterUserType = {
  _id: {
    type: String,
    required: true,
  },
  preregister_id: {
    type: String,
    required: true,
  },
  is_remarketing: {
    type: Boolean,
    default: true,
  },
  fields: {
    type: Object,
    required: true,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
