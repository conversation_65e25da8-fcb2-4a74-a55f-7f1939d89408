import { imagesSchema } from '@/cross-cutting/infrastructure/database/schemas/ImagesSchema';
import { Atmosphere } from '@/events/events/domain/value-objects/Atmosphere';
import { EventService } from '@/events/events/domain/value-objects/EventService';
import { Hanger } from '@/events/events/domain/value-objects/Hanger';
import { MusicalGenre } from '@/events/events/domain/value-objects/MusicalGenre';

export const eventSchema = {
  _id: {
    type: String,
    required: true,
  },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  negocio_nombre: { type: String },
  ubicacion_id: {
    type: String,
    required: true,
  },
  tz_name: { type: String },
  provincia: { type: String },
  provincia_slug: {
    type: String,
    index: true,
    default: '',
  },
  municipio: { type: String },
  weather: { type: Object },
  serie: { type: String },
  codigo: {
    type: String,
    index: true,
  },
  fecha: {
    type: Number,
    required: true,
    index: true,
  },
  dia: {
    type: Number,
    index: true,
  },
  inicio: {
    type: Number,
    required: true,
    index: true,
  },
  fin: {
    type: Number,
    required: true,
    index: true,
  },
  nombre: { type: String },
  slug: {
    type: String,
    index: true,
  },
  descripcion: { type: String },
  edad: {
    type: Number,
    default: 18,
  },
  autorizacion_paterna: { type: String },
  // Si no está activo será un evento en borrador
  activo: {
    type: Boolean,
    default: true,
    index: true,
  },
  // Visible o no desde clientes
  visible: {
    type: Boolean,
    default: true,
  },
  is_private: {
    type: Boolean,
    default: false,
    index: true,
  },
  cancelado: {
    type: Number,
    default: 0,
    index: true,
  },
  plan: {
    type: Number,
    default: 0,
  },
  sms_sent: {
    type: Number,
    default: 0,
  },
  currency: { type: String },
  funciones: {
    type: [String],
    required: false,
  },
  servicios: {
    type: [String],
    required: false,
    default: [],
    enum: EventService.values(),
  },
  percha: {
    type: String,
    default: Hanger.default(),
    enum: Hanger.values(),
  },
  ambiente: {
    type: [String],
    enum: Atmosphere.values(),
  },
  generos: {
    type: [String],
    enum: MusicalGenre.values(),
  },
  artistas: { type: [String] },
  variable_discocil: { type: Number },
  sms_price: { type: Number },
  cambio_nombre_price: { type: Number },
  aplicar_comision_evento: {
    type: Boolean,
    default: false,
  },
  procesando: {
    type: Boolean,
    default: false,
    index: true,
  },
  procesado: {
    type: Boolean,
    default: false,
    index: true,
  },
  fotos_num: {
    type: Number,
    default: 0,
    index: true,
  },
  demo: {
    type: Boolean,
    default: false,
    index: true,
  },
  imagen: { type: String },
  negocio_imagen: { type: String },
  images: {
    type: imagesSchema,
    default: {},
  },
  ggdd: { type: Object },
  imagen_plano: {
    type: String,
    default: '',
  },
  show_imagen_plano: {
    type: Boolean,
    default: true,
  },
  fecha_limite_venta: { type: Number },
  qr_menu_id: {
    type: String,
    default: '',
  },
  show_location: {
    type: Boolean,
    default: true,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
