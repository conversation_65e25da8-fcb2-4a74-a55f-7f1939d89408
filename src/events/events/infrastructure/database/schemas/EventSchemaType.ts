import type { ImagesSchema } from '@/cross-cutting/infrastructure/database/schemas/ImagesSchema';
import type {
  EAtmosphere,
  ECurrency,
  EEventServices,
  EHanger,
  EMusicalGenres,
  UnknownObject,
} from '@discocil/fv-domain-library/domain';

export type EventSchemaType = {
  _id: string;
  negocio_id: string;
  inicio: number;
  fin: number;
  negocio_nombre: string;
  ubicacion_id: string;
  tz_name: string | null;
  provincia: string | null;
  provincia_slug: string | null;
  municipio: string;
  weather?: object | null;
  serie: string;
  codigo: string;
  fecha: number;
  dia: number;
  nombre: string;
  slug: string;
  descripcion: string | null;
  edad: number;
  autorizacion_paterna?: string;
  activo: boolean;
  visible: boolean;
  is_private: boolean;
  cancelado: number;
  plan: number;
  sms_sent: number;
  currency: ECurrency;
  funciones: string[];
  servicios: EEventServices[];
  percha: EHanger;
  ambiente: EAtmosphere[];
  generos: EMusicalGenres[];
  artistas: string[];
  variable_discocil: number;
  sms_price: number;
  cambio_nombre_price: number;
  aplicar_comision_evento: boolean;
  procesando: boolean;
  procesado: boolean;
  fotos_num: number;
  demo: boolean;
  imagen: string | null;
  negocio_imagen: string;
  images: ImagesSchema | UnknownObject;
  ggdd: object;
  imagen_plano: string | null;
  show_imagen_plano: boolean;
  fecha_limite_venta?: number;
  qr_menu_id: string;
  show_location: boolean;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
  preregister: {
    _id: string;
    is_active: boolean;
    endDate: number;
  } | null;
};

export type EventSchemaOriginalType = Omit<EventSchemaType, 'preregister'>;
