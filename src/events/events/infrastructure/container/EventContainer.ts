import { PerformanceMeasurementDecorator } from '@discocil/fv-domain-library/application';
import { container, instanceCachingFactory } from 'tsyringe';

import { BillingAddressDependencyIdentifier } from '@/billingAddresses/domain/dependencyIdentifier/BillingAddressDependencyIdentifier';
import { BlockDependencyIdentifier } from '@/blocks/domain/dependencyIdentifier/BlockDependencyIdentifier';
import { BookingTypeDependencyIdentifier } from '@/bookings/bookingTypes/domain/dependencyIdentifier/BookingTypeDependencyIdentifier';
import { BookingDependencyIdentifier } from '@/bookings/bookings/domain/dependencyIdentifier/BookingDependencyIdentifier';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { ArtistDependencyIdentifier } from '@/events/artists/domain/dependencyIdentifier/ArtistDependencyIdentifier';
import { EventConfigurationCacheRepository } from '@/events/eventConfigurations/infrastructure/database/repositories/EventConfigurationCacheRepository';
import { EventConfigurationMongoRepository } from '@/events/eventConfigurations/infrastructure/database/repositories/EventConfigurationMongoRepository';
import { FindBookingSpaceAvailabilityUseCase } from '@/events/events/application/FindBookingSpaceAvailabilityUseCase';
import { FindEventUseCase } from '@/events/events/application/FindEventUseCase';
import { FindEventZonesWebImageUseCase } from '@/events/events/application/FindEventZonesWebImageUseCase';
import { EventZonesWebImageFinder } from '@/events/events/domain/services/EventZonesWebImageFinder';
import { EventZonesWebImageHttpRepository } from '@/events/events/infrastructure/database/repositories/EventZonesWebImageHttpRepository.ts';
import { EventVisitedUseCase } from '@/events/visits/application/use-cases/EventVisitedUseCase';
import { VisitDependencyIdentifier } from '@/events/visits/domain/dependencyIdentifier/VisitDependencyIdentifier';
import { SearchFeesUseCase } from '@/fees/application/SearchFeesUseCase';
import { ApplyFeesToTicketTypes } from '@/fees/domain/services/ApplyFeesToTicketTypes';
import { GetPriceWithFeesCalculator } from '@/fees/domain/services/GetPriceWithFeesCalculator';
import { GuestListLimitDependencyIdentifier } from '@/guestLists/guestListLimits/domain/dependencyIdentifier/GuestListLimitDependencyIdentifier';
import { GuestListOrganizationLimitDependencyIdentifier } from '@/guestLists/guestListOrganizationLimits/domain/dependencyIdentifier/GuestListOrganizationLimitDependencyIdentifier';
import { GuestListDependencyIdentifier } from '@/guestLists/guestLists/domain/dependencyIdentifier/GuestListDependencyIdentifier';
import { GuestListTypeDependencyIdentifier } from '@/guestLists/guestListsTypes/domain/dependencyIdentifier/GuestListTypeDependencyIdentifier';
import { LocationDependencyIdentifier } from '@/locations/domain/dependencyIdentifier/LocationDependencyIdentifier';
import { LocationByEventsSearcher } from '@/locations/domain/services/LocationByEventsSearcher';
import { LocationFinder } from '@/locations/domain/services/LocationFinder';
import { MicrositeDependencyIdentifier } from '@/microsite/domain/dependencyIdentifier/MicrositeDependencyIdentifier';
import { OrganizationConfigurationFinder } from '@/organizations/organizationConfigurations/domain/services/OrganizationConfigurationFinder';
import { OrganizationDependencyIdentifier } from '@/organizations/organizations/domain/dependencyIdentifier/OrganizationDependencyIdentifier';
import { TicketTypeDependencyIdentifier } from '@/tickets/ticketsTypes/domain/dependencyIdentifier/TicketTypeDependencyIdentifier';
import { UserDependencyIdentifier } from '@/user/domain/dependencyIdentifier/UserDependencyIdentifier';
import config from '@config/index';

import { FetchAllPagesService } from '../../../../cross-cutting/infrastructure/services/FetchAllPagesService';
import { FindEventMetadataUseCase } from '../../application/FindEventMetadataUseCase';
import { RegisterPreregisterUseCase } from '../../application/RegisterPreregisterUseCase';
import { SearchEventBookingZonesUseCase } from '../../application/SearchEventBookingZonesUseCase';
import { SearchEventGuestListTypesUseCase } from '../../application/SearchEventGuestListTypesUseCase';
import { SearchEventTicketTypesWithFeesUseCase } from '../../application/SearchEventTicketTypesWithFeesUseCase';
import { SearchEventTicketsTypesUseCase } from '../../application/SearchEventTicketsTypesUseCase';
import { SearchEventsForSitemapUseCase } from '../../application/SearchEventsForSitemapUseCase';
import { SearchEventsUseCase } from '../../application/SearchEventsUseCase';
import { EventDependencyIdentifier } from '../../domain/dependencyIdentifier/EventDependencyIdentifier';
import { EventForMicrositeFactory } from '../../domain/services/EventForMicrositeFactory';
import { EventsForMicrositeEventGroupsService } from '../../domain/services/EventsForMicrositeEventGroupsService';
import { EventsForMicrositeOrganizationService } from '../../domain/services/EventsForMicrositeOrganizationService';
import { EventsForMicrositeReferrerService } from '../../domain/services/EventsForMicrositeReferrerService';
import { AssignBookingRatesToSpacesService } from '../../domain/services/bookingSpaceAvailability/AssignBookingRatesToSpacesService';
import { IsEventOwnedByOrganizationService } from '../../domain/services/bookingSpaceAvailability/IsEventOwnedByOrganizationService';
import { EventCacheRepository } from '../database/repositories/EventCacheRepository';
import { EventGroupCacheRepository } from '../database/repositories/EventGroupCacheRepository';
import { EventGroupMongoRepository } from '../database/repositories/EventGroupMongoRepository';
import { EventMongoRepository } from '../database/repositories/EventMongoRepository';
import { AssignGuestTypeLimitsService } from '../services/guestListTypes/AssignGuestTypeLimitsService';
import { GetGuestListTypeLimitsService } from '../services/guestListTypes/GetGuestListTypeLimitsService';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Logger } from '@discocil/fv-domain-library/domain';
import type { DependencyContainer } from 'tsyringe';
import type { EventRepository } from '../../domain/contracts/EventRepository';
import type { EventGroupRepository } from '../../domain/contracts/eventGroup/EventGroupRepository';

export const EventContainer = {
  register: (): void => {
    container.register(EventDependencyIdentifier.EventConfigurationRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);
        const mongoRepository = new EventConfigurationMongoRepository(dbConnection);

        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheConfig = container.resolve<CacheConfig>(DependencyIdentifier.CacheConfig);

        return new EventConfigurationCacheRepository(cacheHander, cacheConfig, mongoRepository);
      }),
    });

    container.register(EventDependencyIdentifier.EventRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);
        const mongoRepository = new EventMongoRepository(dbConnection);

        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheConfig = container.resolve<CacheConfig>(DependencyIdentifier.CacheConfig);

        return new EventCacheRepository(cacheHander, cacheConfig, mongoRepository);
      }),
    });

    container.register(EventDependencyIdentifier.EventZonesWebImageRepository, {
      useFactory: instanceCachingFactory((container) => {
        const { apiKey, url } = config.fv.services.reservations;
        const httpRepository = new EventZonesWebImageHttpRepository(apiKey, url, container.resolve(DependencyIdentifier.HttpRepository));

        return httpRepository;
      }),
    });

    container.register(EventDependencyIdentifier.EventGroupRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);
        const mongoRepository = new EventGroupMongoRepository(dbConnection);

        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheConfig = container.resolve<CacheConfig>(DependencyIdentifier.CacheConfig);

        return new EventGroupCacheRepository(cacheHander, cacheConfig, mongoRepository);
      }),
    });

    container.register(EventDependencyIdentifier.IGetGuestListTypeLimitsService, {
      useFactory: instanceCachingFactory((container) => {
        return new GetGuestListTypeLimitsService(
          container.resolve(GuestListDependencyIdentifier.GuestListRepository),
          container.resolve(GuestListLimitDependencyIdentifier.GuestListLimitRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          container.resolve(GuestListOrganizationLimitDependencyIdentifier.GuestListOrganizationLimitRepository),
        );
      }),
    });

    container.register(EventDependencyIdentifier.IAssignGuestTypeLimitsService, {
      useFactory: instanceCachingFactory((container) => {
        return new AssignGuestTypeLimitsService(
          container.resolve(GuestListDependencyIdentifier.GuestListRepository),
          container.resolve(EventDependencyIdentifier.IGetGuestListTypeLimitsService),
        );
      }),
    });

    container.register(FindEventUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const locationFinder = new LocationFinder(container.resolve(LocationDependencyIdentifier.LocationRepository));

        const organizationConfigurationFinder = new OrganizationConfigurationFinder(
          container.resolve(OrganizationDependencyIdentifier.OrganizationConfigurationRepository),
          container.resolve(SearchFeesUseCase),
        );

        const useCase = new FindEventUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(EventDependencyIdentifier.EventConfigurationRepository),
          locationFinder,
          container.resolve(ArtistDependencyIdentifier.ArtistRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          organizationConfigurationFinder,
          container.resolve(BillingAddressDependencyIdentifier.BillingAddressRepository),
          container.resolve(MicrositeDependencyIdentifier.MicrositeRepository),
          container.resolve(MicrositeDependencyIdentifier.MicrositeChannelService),
        );

        return new PerformanceMeasurementDecorator(
          FindEventUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(SearchEventBookingZonesUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new SearchEventBookingZonesUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          container.resolve(MicrositeDependencyIdentifier.MicrositeChannelService),
          container.resolve(MicrositeDependencyIdentifier.MicrositeEventChannelValidation),
          container.resolve(BookingDependencyIdentifier.IGetBookingsServices),
        );

        return new PerformanceMeasurementDecorator(
          SearchEventBookingZonesUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(SearchEventGuestListTypesUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new SearchEventGuestListTypesUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          container.resolve(MicrositeDependencyIdentifier.MicrositeChannelService),
          container.resolve(MicrositeDependencyIdentifier.MicrositeEventChannelValidation),
          container.resolve(GuestListTypeDependencyIdentifier.IGetGuestListTypesAvailableService),
        );

        return new PerformanceMeasurementDecorator(
          SearchEventGuestListTypesUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(SearchEventTicketsTypesUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new SearchEventTicketsTypesUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          container.resolve(MicrositeDependencyIdentifier.MicrositeChannelService),
          container.resolve(MicrositeDependencyIdentifier.MicrositeEventChannelValidation),
          container.resolve(TicketTypeDependencyIdentifier.TicketTypeRepository),
          container.resolve(TicketTypeDependencyIdentifier.IGetTicketTypesExtrasService),
        );

        return new PerformanceMeasurementDecorator(
          SearchEventTicketsTypesUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(SearchEventTicketTypesWithFeesUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const getPriceWithFeesCalculator = new GetPriceWithFeesCalculator();
        const applyFeesToTicketTypes = new ApplyFeesToTicketTypes(getPriceWithFeesCalculator);

        const useCase = new SearchEventTicketTypesWithFeesUseCase(
          container.resolve(SearchEventTicketsTypesUseCase),
          container.resolve(OrganizationDependencyIdentifier.OrganizationConfigurationRepository),
          container.resolve(SearchFeesUseCase),
          applyFeesToTicketTypes,
        );

        return new PerformanceMeasurementDecorator(
          SearchEventTicketTypesWithFeesUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(SearchEventsUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const eventsLocationSearcher = new LocationByEventsSearcher(container.resolve(LocationDependencyIdentifier.LocationRepository));

        const eventRepository = container.resolve<EventRepository>(EventDependencyIdentifier.EventRepository);
        const organizationRepository = container.resolve<OrganizationRepository>(OrganizationDependencyIdentifier.OrganizationRepository);
        const eventGroupRepository = container.resolve<EventGroupRepository>(EventDependencyIdentifier.EventGroupRepository);

        const eventForMicrositeOrganizationService = new EventsForMicrositeOrganizationService(eventRepository, organizationRepository);
        const eventForMicrositeReferrerService = new EventsForMicrositeReferrerService(eventRepository, organizationRepository);
        const eventsForMicrositeEventGroupsService = new EventsForMicrositeEventGroupsService(eventRepository, eventGroupRepository);

        const eventMicrositeFactory = new EventForMicrositeFactory(
          eventForMicrositeOrganizationService,
          eventForMicrositeReferrerService,
          eventsForMicrositeEventGroupsService,
        );

        const useCase = new SearchEventsUseCase(
          container.resolve(MicrositeDependencyIdentifier.MicrositeChannelService),
          container.resolve(BillingAddressDependencyIdentifier.BillingAddressRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          container.resolve(ArtistDependencyIdentifier.ArtistRepository),
          container.resolve(BlockDependencyIdentifier.BlockRepository),
          eventsLocationSearcher,
          eventMicrositeFactory,
        );

        return new PerformanceMeasurementDecorator(
          SearchEventsUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(SearchEventsForSitemapUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new SearchEventsForSitemapUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(BillingAddressDependencyIdentifier.BillingAddressRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
        );

        return new PerformanceMeasurementDecorator(
          SearchEventsForSitemapUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(EventVisitedUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new EventVisitedUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(UserDependencyIdentifier.UserRepository),
          container.resolve(VisitDependencyIdentifier.VisitRepository),
        );

        return new PerformanceMeasurementDecorator(
          EventVisitedUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(RegisterPreregisterUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new RegisterPreregisterUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(DependencyIdentifier.ExternalMessageBrokerClient),
          container.resolve(DependencyIdentifier.InternalMessageBrokerClient),
        );

        return new PerformanceMeasurementDecorator(
          RegisterPreregisterUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(FindEventZonesWebImageUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const eventZonesWebImageFinder = new EventZonesWebImageFinder(container.resolve(EventDependencyIdentifier.EventZonesWebImageRepository));

        const useCase = new FindEventZonesWebImageUseCase(eventZonesWebImageFinder);

        return new PerformanceMeasurementDecorator(
          FindEventZonesWebImageUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(FindBookingSpaceAvailabilityUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const isEventOwnedByOrganizationService = new IsEventOwnedByOrganizationService(
          container.resolve(EventDependencyIdentifier.EventRepository),
        );

        const assignBookingRatesToSpacesService = new AssignBookingRatesToSpacesService(
          container.resolve(BookingTypeDependencyIdentifier.BookingTypeRepository),
        );

        const useCase = new FindBookingSpaceAvailabilityUseCase(
          container.resolve(UserDependencyIdentifier.UserRepository),
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          isEventOwnedByOrganizationService,
          assignBookingRatesToSpacesService,
          container.resolve(BookingDependencyIdentifier.IGetBookingsServices),
        );

        return new PerformanceMeasurementDecorator(
          FindBookingSpaceAvailabilityUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(FindEventMetadataUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const locationFinder = new LocationFinder(container.resolve(LocationDependencyIdentifier.LocationRepository));
        const fetchAllPagesFromUseCase = new FetchAllPagesService();

        const useCase = new FindEventMetadataUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          locationFinder,
          container.resolve(ArtistDependencyIdentifier.ArtistRepository),
          container.resolve(OrganizationDependencyIdentifier.OrganizationRepository),
          container.resolve(BillingAddressDependencyIdentifier.BillingAddressRepository),
          container.resolve(SearchEventTicketsTypesUseCase),
          container.resolve(SearchEventGuestListTypesUseCase),
          fetchAllPagesFromUseCase,
        );

        return new PerformanceMeasurementDecorator(
          FindEventUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });
  },
};
