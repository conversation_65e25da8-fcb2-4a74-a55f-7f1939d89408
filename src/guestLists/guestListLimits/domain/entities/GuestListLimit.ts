import {
  AggregateRoot,
  UniqueEntityID,
  right,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type GuestListLimitPrimitives = Primitives<GuestListLimit>;

export type GuestListLimits = Map<IdPrimitive, GuestListLimit>;

export type GuestListLimitEither = Either<NotFoundError | MapperError, GuestListLimit>;
export type GuestListLimitsEither = Either<NotFoundError | MapperError, GuestListLimits>;

export type GuestListLimitKeys = keyof GuestListLimit;

export class GuestListLimit extends AggregateRoot {
  static readonly maxSimultaneous = 99;

  private constructor(
    id: UniqueEntityID,
    readonly typeSlug: string,
    private readonly _organizationId: Maybe<UniqueEntityID>,
    private readonly _eventId: UniqueEntityID,
    private readonly _userId: Maybe<UniqueEntityID>,
    readonly maximum: number,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: GuestListLimitPrimitives): GuestListLimitEither {
    const id = UniqueEntityID.build(primitives.id);
    const eventId = UniqueEntityID.build(primitives.eventId);
    const organizationId = primitives.organizationId.map(item => UniqueEntityID.build(item));
    const userId = primitives.userId.map(item => UniqueEntityID.build(item));

    const stamps = stampValueObjects(primitives);

    const entity = new GuestListLimit(
      id,
      primitives.typeSlug,
      organizationId,
      eventId,
      userId,
      primitives.maximum,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): Maybe<IdPrimitive> {
    return this._organizationId.map(id => id.toPrimitive());
  }

  get eventId(): IdPrimitive {
    return this._eventId.toPrimitive();
  }

  get userId(): Maybe<IdPrimitive> {
    return this._userId.map(id => id.toPrimitive());
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  toPrimitives(): GuestListLimitPrimitives {
    return {
      id: this.id,
      typeSlug: this.typeSlug,
      organizationId: this.organizationId,
      eventId: this.eventId,
      userId: this.userId,
      maximum: this.maximum,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
