import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { GuestListLimitKeys } from '../entities/GuestListLimit';

class FilterField extends FilterFieldBase<GuestListLimitKeys> {}

export class UserIdFilter {
  private static readonly field: GuestListLimitKeys = 'userId';

  static buildEqual(userId: UniqueEntityID): Filter {
    const userIdField = new FilterField(this.field);
    const userIdOperator = FilterOperator.equal();
    const userIdFilterValue = FilterValue.build(userId.value);

    return new Filter(userIdField, userIdOperator, userIdFilterValue);
  }
}
