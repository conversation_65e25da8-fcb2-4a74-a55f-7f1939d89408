import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { GuestListLimitKeys } from '../entities/GuestListLimit';

class FilterField extends FilterFieldBase<GuestListLimitKeys> {}

export class RateSlugFilter {
  private static readonly field: GuestListLimitKeys = 'typeSlug';

  static buildEqual(slug: string): Filter {
    const slugField = new FilterField(this.field);
    const slugOperator = FilterOperator.equal();
    const slugFilterValue = FilterValue.build(slug);

    return new Filter(slugField, slugOperator, slugFilterValue);
  }
}
