import {
  Criteria,
  Filters,
  Order,
} from '@discocil/fv-criteria-converter-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { RateSlugFilter } from './RateSlugFilter';
import { UserIdFilter } from './UserIdFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { GuestListLimitKeys } from '../entities/GuestListLimit';

export class GuestListLimitCriteriaMother {
  private static readonly orderField: GuestListLimitKeys = 'maximum';

  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static specificLimitToMatch(eventId: UniqueEntityID, slug: string, referrerId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(RateSlugFilter.buildEqual(slug));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(UserIdFilter.buildEqual(referrerId));
    // filters.add(OrganizationIdFilter.buildEmpty());

    return Criteria.build(filters, Order.desc<GuestListLimitKeys>(this.orderField));
  }

  static personalLimitToMatch(eventId: UniqueEntityID, slug: string, organizationGroups: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(RateSlugFilter.buildEqual(slug));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(OrganizationIdFilter.buildIn(organizationGroups));

    return Criteria.build(filters, Order.desc<GuestListLimitKeys>(this.orderField));
  }

  static organizationAssignedLimitToMatch(eventId: UniqueEntityID, slug: string, organizationAssignedId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(RateSlugFilter.buildEqual(slug));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(OrganizationIdFilter.buildEqual(organizationAssignedId));
    filters.add(EventIdFilter.buildEqual(eventId));

    return Criteria.build(filters, Order.desc<GuestListLimitKeys>(this.orderField));
  }
}
