import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type {
  GuestListLimit,
  GuestListLimitEither,
  GuestListLimits,
  GuestListLimitsEither,
} from '../entities/GuestListLimit';

export interface GuestListLimitRepository {
  find: (criteria: Criteria) => Promise<GuestListLimitEither>;
  save: (guestListLimit: GuestListLimit) => Promise<void>;
  saveMany: (guestListLimit: GuestListLimits) => Promise<void>;
  search: (criteria: Criteria) => Promise<GuestListLimitsEither>;
}
