

import type { GuestListLimit } from '@/guestLists/guestListLimits/domain/entities/GuestListLimit';
import type { GuestListLimitSchemaType } from '../schemas/GuestListLimitSchemaType';

export class GuestListLimitSchemaMapper {
  static execute(guestListLimit: GuestListLimit): GuestListLimitSchemaType {
    return {
      _id: guestListLimit.id,
      tarifa_slug: guestListLimit.typeSlug,
      negocio_id: guestListLimit.organizationId.fold(() => undefined, item => item),
      evento_id: guestListLimit.eventId,
      usuario_id: guestListLimit.userId.fold(() => undefined, item => item),
      maximo: guestListLimit.maximum,
      created_at: guestListLimit.createdAt,
      created_by: guestListLimit.createdBy,
      updated_at: guestListLimit.updatedAt,
      updated_by: guestListLimit.updatedBy,
      removed_at: guestListLimit.removedAt,
      removed_by: guestListLimit.removedBy,
    };
  }
}
