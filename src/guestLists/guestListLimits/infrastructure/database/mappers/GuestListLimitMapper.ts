import {
  FvDate, Maybe, left,
} from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { GuestListLimit } from '@/guestLists/guestListLimits/domain/entities/GuestListLimit';

import { guestListLimitValidationSchema } from './GuestListLimitSchemaValidation';

import type { GuestListLimitEither } from '@/guestLists/guestListLimits/domain/entities/GuestListLimit';
import type Ajv from 'ajv';
import type { GuestListLimitSchemaType } from '../schemas/GuestListLimitSchemaType';

export class GuestListLimitMapper {
  static execute(data: GuestListLimitSchemaType): GuestListLimitEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(guestListLimitValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: GuestListLimitMapper.name,
        data,
        target: validate.errors,
      });

      return GuestListLimitSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: GuestListLimitSchemaType): GuestListLimitEither {
    return GuestListLimit.build({
      id: data._id,
      typeSlug: data.tarifa_slug,
      organizationId: Maybe.fromValue(data.negocio_id),
      eventId: data.evento_id,
      userId: Maybe.fromValue(data.usuario_id),
      maximum: data.maximo ?? 0,
      createdAt: data.created_at ?? FvDate.create().toMilliseconds(),
      createdBy: data.created_by,
      updatedAt: data.updated_at ?? FvDate.create().toMilliseconds(),
      updatedBy: data.updated_by,
      removedAt: data.removed_at ?? FvDate.create().toMilliseconds(),
      removedBy: data.removed_by,
    });
  }
}
export class GuestListLimitSoftMapper extends GuestListLimitMapper {
  static execute(data: GuestListLimitSchemaType): GuestListLimitEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(guestListLimitValidationSchema);

    if (!validate(data) || validate.errors) {
      return left(MapperError.build({
        context: GuestListLimitMapper.name,
        data,
        target: validate.errors,
      }));
    }

    return this.buildEntity({
      ...data,
      _id: data._id,
      tarifa_slug: data.tarifa_slug ?? null,
      evento_id: data.evento_id ?? null,
    });
  }
}
