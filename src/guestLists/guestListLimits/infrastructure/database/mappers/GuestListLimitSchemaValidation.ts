import type { JSONSchemaType } from 'ajv';
import type { GuestListLimitSchemaType } from '../schemas/GuestListLimitSchemaType';

export const guestListLimitValidationSchema: JSONSchemaType<GuestListLimitSchemaType> = {
  title: 'GuestListType Json Schema Validation',
  required: ['_id', 'tarifa_slug', 'evento_id', 'maximo'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    tarifa_slug: { type: 'string' },
    negocio_id: { type: 'string', nullable: true },
    evento_id: { type: 'string' },
    usuario_id: { type: 'string', nullable: true },
    maximo: { type: 'number' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
