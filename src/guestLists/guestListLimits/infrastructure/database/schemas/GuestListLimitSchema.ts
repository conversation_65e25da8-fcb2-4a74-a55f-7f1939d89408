import { FvDate } from '@discocil/fv-domain-library/domain';

export const guestListLimitSchema = {
  _id: { type: String },
  tarifa_slug: {
    type: String,
    required: true,
    index: true,
  },
  negocio_id: {
    type: String,
    index: true,
  },
  evento_id: {
    type: String,
    index: true,
  },
  usuario_id: {
    type: String,
    index: true,
  },
  maximo: {
    type: Number,
    default: 0,
  },
  created_at: {
    type: Number,
    default: FvDate.create().toMilliseconds(),
    index: true,
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_at: {
    type: Number,
    default: FvDate.create().toMilliseconds(),
    index: true,
  },
  updated_by: {
    type: String,
    default: '',
  },
  removed_at: {
    type: Number,
    default: 0,
    index: true,
  },
  removed_by: {
    type: String,
    default: '',
    select: false,
  },
};
