import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { GuestListLimitDependencyIdentifier } from '../../domain/dependencyIdentifier/GuestListLimitDependencyIdentifier';
import { GuestListLimitMongoRepository } from '../database/repositories/GuestListLimitMongoRepository';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const GuestListLimitContainer = {
  register: (): void => {
    container.register(GuestListLimitDependencyIdentifier.GuestListLimitRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new GuestListLimitMongoRepository(dbConnection);
      }),
    });
  },
};
