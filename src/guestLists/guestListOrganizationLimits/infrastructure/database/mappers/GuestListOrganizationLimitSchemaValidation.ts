import type { JSONSchemaType } from 'ajv';
import type { GuestListOrganizationLimitSchemaType } from '../schemas/GuestListOrganizationLimitSchemaType';

export const guestListOrganizationLimitValidationSchema: JSONSchemaType<GuestListOrganizationLimitSchemaType> = {
  title: 'GuestListType Json Schema Validation',
  required: ['_id', 'negocio_id', 'negocio_limited_id', 'list_type_id', 'event_id', 'limit'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    negocio_limited_id: { type: 'string' },
    list_type_id: { type: 'string' },
    event_id: { type: 'string' },
    limit: { type: 'number' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
