import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { GuestListOrganizationLimitDependencyIdentifier } from '../../domain/dependencyIdentifier/GuestListOrganizationLimitDependencyIdentifier';
import { GuestListOrganizationLimitMongoRepository } from '../database/repositories/GuestListOrganizationLimitMongoRepository';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const GuestListOrganizationLimitContainer = {
  register: (): void => {
    container.register(GuestListOrganizationLimitDependencyIdentifier.GuestListOrganizationLimitRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new GuestListOrganizationLimitMongoRepository(dbConnection);
      }),
    });
  },
};
