import {
  AggregateRoot,
  UniqueEntityID,
  right,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  IdPrimitive,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type GuestListOrganizationLimitPrimitives = Primitives<GuestListOrganizationLimit>;

export type GuestListOrganizationLimits = Map<IdPrimitive, GuestListOrganizationLimit>;

export type GuestListOrganizationLimitEither = Either<NotFoundError | MapperError, GuestListOrganizationLimit>;
export type GuestListOrganizationLimitsEither = Either<NotFoundError | MapperError, GuestListOrganizationLimits>;

export type GuestListOrganizationLimitKeys = keyof GuestListOrganizationLimit;

export class GuestListOrganizationLimit extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    private readonly _organizationLimitedId: UniqueEntityID,
    private readonly _typeId: UniqueEntityID,
    private readonly _eventId: UniqueEntityID,
    readonly limit: number,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: GuestListOrganizationLimitPrimitives): GuestListOrganizationLimitEither {
    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const organizationLimitedId = UniqueEntityID.build(primitives.organizationLimitedId);
    const typeId = UniqueEntityID.build(primitives.typeId);
    const eventId = UniqueEntityID.build(primitives.eventId);

    const stamps = stampValueObjects(primitives);

    const entity = new GuestListOrganizationLimit(
      id,
      organizationId,
      organizationLimitedId,
      typeId,
      eventId,
      primitives.limit,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get organizationLimitedId(): IdPrimitive {
    return this._organizationLimitedId.toPrimitive();
  }

  get typeId(): IdPrimitive {
    return this._typeId.toPrimitive();
  }

  get eventId(): IdPrimitive {
    return this._eventId.toPrimitive();
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  toPrimitives(): GuestListOrganizationLimitPrimitives {
    return {
      id: this.id,
      organizationId: this.organizationId,
      organizationLimitedId: this.organizationLimitedId,
      typeId: this.typeId,
      eventId: this.eventId,
      limit: this.limit,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
