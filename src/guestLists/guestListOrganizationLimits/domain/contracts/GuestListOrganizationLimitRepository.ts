import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type {
  GuestListOrganizationLimit,
  GuestListOrganizationLimitEither,
  GuestListOrganizationLimits,
  GuestListOrganizationLimitsEither,
} from '../entities/GuestListOrganizationLimit';

export interface GuestListOrganizationLimitRepository {
  find: (criteria: Criteria) => Promise<GuestListOrganizationLimitEither>;
  save: (guestListType: GuestListOrganizationLimit) => Promise<void>;
  saveMany: (guestListType: GuestListOrganizationLimits) => Promise<void>;
  search: (criteria: Criteria) => Promise<GuestListOrganizationLimitsEither>;
}
