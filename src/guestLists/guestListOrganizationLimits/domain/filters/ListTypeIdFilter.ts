import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { GuestListOrganizationLimitKeys } from '../entities/GuestListOrganizationLimit';

class FilterField extends FilterFieldBase<GuestListOrganizationLimitKeys> {}

export class ListTypeIdFilter {
  private static readonly field: GuestListOrganizationLimitKeys = 'typeId';

  static buildEqual(listTypeId: UniqueEntityID): Filter {
    const listTypeIdField = new FilterField(this.field);
    const listTypeIdOperator = FilterOperator.equal();
    const listTypeIdFilterValue = FilterValue.build(listTypeId.value);

    return new Filter(listTypeIdField, listTypeIdOperator, listTypeIdFilterValue);
  }
}
