import {
  Criteria,
  Filters,
  Order,
} from '@discocil/fv-criteria-converter-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';

import { ListTypeIdFilter } from './ListTypeIdFilter';
import { OrganizationLimitedIdFilter } from './OrganizationLimitedIdFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { GuestListOrganizationLimitKeys } from '../entities/GuestListOrganizationLimit';

export class GuestListOrganizationLimitCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));

    return Criteria.build(filters);
  }

  static groupLimitToMatch(
    guestListTypeId: UniqueEntityID,
    eventId: UniqueEntityID,
    organizationGroups: UniqueEntityID[],
    organizationId: UniqueEntityID,
  ): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(ListTypeIdFilter.buildEqual(guestListTypeId));
    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(OrganizationLimitedIdFilter.buildIn(organizationGroups));

    return Criteria.build(filters, Order.desc<GuestListOrganizationLimitKeys>('limit'));
  }
}
