import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { GuestListOrganizationLimitKeys } from '../entities/GuestListOrganizationLimit';

class FilterField extends FilterFieldBase<GuestListOrganizationLimitKeys> {}

export class OrganizationLimitedIdFilter {
  private static readonly field: GuestListOrganizationLimitKeys = 'organizationLimitedId';

  static buildIn(organizationsIds: UniqueEntityID[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();
    const filterValues: FilterValue[] = organizationsIds.map((value: UniqueEntityID) => FilterValue.build(value.value));

    return new Filter(field, operator, filterValues);
  }
}
