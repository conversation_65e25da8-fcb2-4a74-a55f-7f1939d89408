import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { EventTracking } from '../entities/EventTracking';
import type { TrackingsEither } from '../services/EventTrackingFactory';
import type { TrackingSaveEither } from './TrackingRepository';

export interface TrackingSaveRepository {
  save: (event: EventTracking) => Promise<TrackingSaveEither>;
}
export interface TrackingSearchRepository {
  search: (criteria: Criteria) => Promise<TrackingsEither>;
}
