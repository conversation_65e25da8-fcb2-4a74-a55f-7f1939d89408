import type {
  Collection,
  FvDate,
  FvNumber,
  Maybe,
  Money,
  Primitives,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

export type TrackingUser = {
  readonly name: Maybe<string>;
  readonly email: Maybe<string>;
  readonly phone: Maybe<string>;
};

export type TrackingFb = {
  readonly fbp: Maybe<string>;
  readonly fbc: Maybe<string>;
  readonly fbclid: Maybe<string>;
};

export type TrackingItem = {
  readonly id: UniqueEntityID;
  readonly price: Money;
  readonly quantity: FvNumber;
  readonly category: string;
};

export type TrackingItemPrimitive = Primitives<TrackingItem>;

export type TrackingContent = {
  readonly id: UniqueEntityID;
  readonly date: FvDate;
  readonly name: string;
  readonly type: string;
  readonly ids: Collection<string>;
};

export type TrackingContentPrimitives = Primitives<Omit<TrackingContent, 'ids'>> & {
  readonly ids: string[];
};

export enum EMicrositeContainerType {
  WEB = 'web',
  IFRAME = 'iframe'
}
