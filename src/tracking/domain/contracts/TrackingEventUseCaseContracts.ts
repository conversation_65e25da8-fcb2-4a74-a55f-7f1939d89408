import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Either,
  EMicrositeServices,
  IdPrimitive,
  InvalidArgumentError,
  Maybe,
  MoneyError,
  MoneyProps,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { InitiateCheckoutContentPrimitives } from '../entities/InitiateCheckout';
import type { PurchaseContentPrimitives } from '../entities/Purchase';
import type { ViewContentContentPrimitives } from '../entities/ViewContent';
import type { MissingCredentialsError } from '../errors/MissingCredentialsError';
import type { UnsupportedEventError } from '../errors/UnsupportedEventError';
import type { EEventChannel, EEventTypes } from '../value-objects/EventType';
import type {
  EMicrositeContainerType,
  TrackingContentPrimitives,
  TrackingFb,
  TrackingItemPrimitive,
  TrackingUser,
} from './EntityContracts';

export type TrackingEventContentDto =
  ViewContentContentPrimitives
  | PurchaseContentPrimitives
  | InitiateCheckoutContentPrimitives
  | TrackingContentPrimitives;

export type TrackingEventDto = {
  readonly channel: EEventChannel;
  readonly name: EEventTypes;
  readonly id: string;
  readonly externalId: Maybe<string>;
  readonly organizationId: string;
  readonly urlPage: string;
  readonly route: Maybe<string>;
  readonly user: Maybe<TrackingUser>;
  readonly fb: Maybe<TrackingFb>;
  readonly content: Maybe<TrackingEventContentDto>;
  readonly price: Maybe<MoneyProps>;
  readonly items: Maybe<TrackingItemPrimitive[]>;
  readonly numItems: Maybe<number>;
  readonly totalPrice: Maybe<MoneyProps>;
  readonly remoteAddress: string;
  readonly userAgent: string;
  readonly eventId: Maybe<IdPrimitive>;
  readonly sessionId: Maybe<string>;
  readonly serviceType: Maybe<EMicrositeServices>;
  readonly containerType: Maybe<EMicrositeContainerType>;
};

export type TrackingEventEither = Either<
  InvalidArgumentError
  | MapperError
  | MissingCredentialsError
  | NotFoundError
  | UnexpectedError
  | UnsupportedEventError
  | MoneyError,
  true
>;
