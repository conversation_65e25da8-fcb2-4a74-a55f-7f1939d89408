import { AggregateRoot } from '@discocil/fv-domain-library/domain';

import type {
  EMicrositeServices, Maybe, UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import type {
  EMicrositeContainerType, TrackingFb, TrackingUser,
} from '../contracts/EntityContracts';
import type { EEventTypes, EventType } from '../value-objects/EventType';

export abstract class EventTracking extends AggregateRoot {
  protected constructor(
    id: UniqueEntityID,
    private readonly _type: EventType,
    private readonly _externalId: Maybe<UniqueEntityID>,
    private readonly _organizationId: UniqueEntityID,
    private readonly _urlPage: URL,
    readonly user: Maybe<TrackingUser>,
    readonly fb: Maybe<TrackingFb>,
    readonly remoteAddress: string,
    readonly userAgent: string,
    private readonly _eventId: Maybe<UniqueEntityID>,
    private readonly _sessionId: Maybe<string>,
    private readonly _serviceType: Maybe<EMicrositeServices>,
    private readonly _containerType: Maybe<EMicrositeContainerType>,
  ) {
    super(id);
  }

  get name(): EEventTypes {
    return this._type.toPrimitive();
  }

  get externalId(): Maybe<string> {
    return this._externalId.map(item => item.toPrimitive());
  }

  get organizationId(): string {
    return this._organizationId.toPrimitive();
  }

  get urlPage(): string {
    return this._urlPage.toString();
  }

  get eventId(): Maybe<string> {
    return this._eventId.map(item => item.toPrimitive());
  }

  get sessionId(): Maybe<string> {
    return this._sessionId;
  }

  get serviceType(): Maybe<EMicrositeServices> {
    return this._serviceType;
  }

  get containerType(): Maybe<EMicrositeContainerType> {
    return this._containerType;
  }

  isPageView(): boolean {
    return this._type.isPageView();
  }
}
