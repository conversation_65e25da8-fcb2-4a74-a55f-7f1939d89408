import {
  Collection,
  FvDate,
  FvNumber,
  left,
  Money,
  right,
  UnexpectedError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EventType } from '../value-objects/EventType';

import { EventTracking } from './EventTracking';

import type {
  Either,
  EMicrositeServices,
  InvalidArgumentError,
  Maybe,
  MoneyError,
  MoneyProps,
  Primitives,
} from '@discocil/fv-domain-library/domain';
import type {
  EMicrositeContainerType,
  TrackingContent,
  TrackingContentPrimitives,
  TrackingFb,
  TrackingItem,
  TrackingItemPrimitive,
  TrackingUser,
} from '../contracts/EntityContracts';

export type AddToCartPrimitives = Primitives<AddToCart>;

type AddToCartEither = Either<InvalidArgumentError | UnexpectedError | MoneyError, AddToCart>;

export class AddToCart extends EventTracking {
  private constructor(
    id: UniqueEntityID,
    externalId: Maybe<UniqueEntityID>,
    organizationId: UniqueEntityID,
    urlPage: URL,
    user: Maybe<TrackingUser>,
    fb: Maybe<TrackingFb>,
    remoteAddress: string,
    userAgent: string,
    type: EventType,
    private readonly _price: Money,
    private readonly _items: Collection<TrackingItem>,
    private readonly _numItems: FvNumber,
    private readonly _totalPrice: Money,
    private readonly _content: TrackingContent,
    eventId: Maybe<UniqueEntityID>,
    sessionId: Maybe<string>,
    serviceType: Maybe<EMicrositeServices>,
    containerType: Maybe<EMicrositeContainerType>,
  ) {
    super(
      id,
      type,
      externalId,
      organizationId,
      urlPage,
      user,
      fb,
      remoteAddress,
      userAgent,
      eventId,
      sessionId,
      serviceType,
      containerType,
    );
  }

  static build(primitives: AddToCartPrimitives): AddToCartEither {
    const priceOrError = Money.build(primitives.price);

    if (priceOrError.isLeft()) {
      return left(priceOrError.value);
    }

    const totalPriceOrError = Money.build(primitives.totalPrice);

    if (totalPriceOrError.isLeft()) {
      return left(totalPriceOrError.value);
    }

    const price = priceOrError.value;
    const totalPrice = totalPriceOrError.value;

    try {
      const id = UniqueEntityID.build(primitives.id);
      const externalId = primitives.externalId.map(item => UniqueEntityID.build(item));
      const organizationId = UniqueEntityID.build(primitives.organizationId);

      const urlPage = new URL(primitives.urlPage);
      const numItems = FvNumber.build(primitives.numItems);

      const user = primitives.user.map(item => item);
      const fb = primitives.fb.map(item => item);

      const items = Collection.new<TrackingItem>('id');

      for (const primitiveItem of primitives.items) {
        const priceOrError = Money.build({
          amount: primitiveItem.price,
          currency: primitives.price.currency,
        });

        if (priceOrError.isLeft()) {
          return left(priceOrError.value);
        }

        const id = UniqueEntityID.build(primitiveItem.id);
        const price = priceOrError.value;
        const quantity = FvNumber.build(primitiveItem.quantity);

        items.add({
          ...primitiveItem,
          id,
          price,
          quantity,
        });
      }

      const content = {
        id: UniqueEntityID.build(primitives.content.id),
        name: primitives.content.name,
        type: primitives.content.type,
        ids: Collection.new<string>('id'),
        date: FvDate.create(primitives.content.date),
      };

      const entity = new AddToCart(
        id,
        externalId,
        organizationId,
        urlPage,
        user,
        fb,
        primitives.remoteAddress,
        primitives.userAgent,
        EventType.AddToCart(),
        price,
        items,
        numItems,
        totalPrice,
        content,
        primitives.eventId.map(item => UniqueEntityID.build(item)),
        primitives.sessionId.map(item => item),
        primitives.serviceType.map(item => item),
        primitives.containerType.map(item => item),
      );

      return right(entity);
    } catch (error) {
      const parsedError = error as Error;

      return left(UnexpectedError.build({
        context: AddToCart.name,
        error: parsedError,
        data: primitives,
      }));
    }
  }

  get price(): MoneyProps {
    return this._price.toPrimitive();
  }

  get items(): TrackingItemPrimitive[] {
    return this._items.toArray().map(item => ({
      ...item,
      id: item.id.toPrimitive(),
      price: item.price.toDecimal(),
      quantity: item.quantity.toPrimitive(),
    }));
  }

  get numItems(): number {
    return this._numItems.toPrimitive();
  }

  get totalPrice(): MoneyProps {
    return this._totalPrice.toPrimitive();
  }

  get content(): TrackingContentPrimitives {
    return {
      ...this._content,
      id: this._content.id.toPrimitive(),
      ids: this._content.ids.toArray(),
      date: this._content.date.toPrimitive(),
    };
  }

  toPrimitives(): AddToCartPrimitives {
    return {
      id: this.id,
      name: this.name,
      externalId: this.externalId,
      organizationId: this.organizationId,
      urlPage: this.urlPage,
      user: this.user,
      fb: this.fb,
      price: this.price,
      items: this.items,
      numItems: this.numItems,
      totalPrice: this.totalPrice,
      content: this.content,
      remoteAddress: this.remoteAddress,
      userAgent: this.userAgent,
      eventId: this.eventId,
      sessionId: this.sessionId,
      serviceType: this.serviceType,
      containerType: this.containerType,
    };
  }
}
