import {
  left,
  right,
  UnexpectedError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EventType } from '../value-objects/EventType';

import { EventTracking } from './EventTracking';

import type {
  Either,
  EMicrositeServices,
  Maybe,
  Primitives,
} from '@discocil/fv-domain-library/domain';
import type {
  EMicrositeContainerType,
  TrackingFb,
  TrackingUser,
} from '../contracts/EntityContracts';

export type PageViewPrimitives = Primitives<PageView>;

type PageViewEither = Either<UnexpectedError, PageView>;

export class PageView extends EventTracking {
  private constructor(
    id: UniqueEntityID,
    externalId: Maybe<UniqueEntityID>,
    organizationId: UniqueEntityID,
    urlPage: URL,
    user: Maybe<TrackingUser>,
    fb: Maybe<TrackingFb>,
    remoteAddress: string,
    userAgent: string,
    type: EventType,
    readonly route: string,
    eventId: Maybe<UniqueEntityID>,
    sessionId: Maybe<string>,
    serviceType: Maybe<EMicrositeServices>,
    containerType: Maybe<EMicrositeContainerType>,
  ) {
    super(
      id,
      type,
      externalId,
      organizationId,
      urlPage,
      user,
      fb,
      remoteAddress,
      userAgent,
      eventId,
      sessionId,
      serviceType,
      containerType,
    );
  }

  static build(primitives: PageViewPrimitives): PageViewEither {
    try {
      const id = UniqueEntityID.build(primitives.id);
      const externalId = primitives.externalId.map(item => UniqueEntityID.build(item));
      const organizationId = UniqueEntityID.build(primitives.organizationId);

      const urlPage = new URL(primitives.urlPage);

      const user = primitives.user.map(item => item);
      const fb = primitives.fb.map(item => item);

      const entity = new PageView(
        id,
        externalId,
        organizationId,
        urlPage,
        user,
        fb,
        primitives.remoteAddress,
        primitives.userAgent,
        EventType.PageView(),
        primitives.route,
        primitives.eventId.map(item => UniqueEntityID.build(item)),
        primitives.sessionId.map(item => item),
        primitives.serviceType.map(item => item),
        primitives.containerType.map(item => item),
      );

      return right(entity);
    } catch (error) {
      const parsedError = error as Error;

      return left(UnexpectedError.build({
        context: PageView.name,
        error: parsedError,
        data: primitives,
      }));
    }
  }

  toPrimitives(): PageViewPrimitives {
    return {
      id: this.id,
      name: this.name,
      externalId: this.externalId,
      organizationId: this.organizationId,
      urlPage: this.urlPage,
      route: this.route,
      user: this.user,
      fb: this.fb,
      remoteAddress: this.remoteAddress,
      userAgent: this.userAgent,
      eventId: this.eventId,
      sessionId: this.sessionId,
      serviceType: this.serviceType,
      containerType: this.containerType,
    };
  }
}
