import {
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { AddToCart } from '../entities/AddToCart';
import { InitiateCheckout } from '../entities/InitiateCheckout';
import { PageView } from '../entities/PageView';
import { Purchase } from '../entities/Purchase';
import { ViewContent } from '../entities/ViewContent';
import { EventType } from '../value-objects/EventType';

import type { PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  IdPrimitive,
  InvalidArgumentError,
  MoneyError,
  Properties,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { TrackingContentPrimitives } from '../contracts/EntityContracts';
import type { TrackingEventDto } from '../contracts/TrackingEventUseCaseContracts';
import type { AddToCartPrimitives } from '../entities/AddToCart';
import type { EventTracking } from '../entities/EventTracking';
import type { InitiateCheckoutContentPrimitives, InitiateCheckoutPrimitives } from '../entities/InitiateCheckout';
import type { PageViewPrimitives } from '../entities/PageView';
import type { PurchaseContentPrimitives, PurchasePrimitives } from '../entities/Purchase';
import type { ViewContentContentPrimitives, ViewContentPrimitives } from '../entities/ViewContent';
import type { UnsupportedEventError } from '../errors/UnsupportedEventError';

export type Trackings = Map<IdPrimitive, EventTracking>;

export type SearchPaginatedTrackings = PaginationMetadataResponse & {
  readonly trackings: Trackings;
};

export type TrackingEither = Either<UnsupportedEventError | InvalidArgumentError | UnexpectedError | MoneyError, EventTracking>;
export type TrackingsEither = Either<UnexpectedError, SearchPaginatedTrackings>;

export type TrackingKeys = keyof Properties<EventTracking>;

export class EventMetaFactory {
  static execute(dto: TrackingEventDto): TrackingEither {
    const eventTypeOrError = EventType.build(dto.name);

    if (eventTypeOrError.isLeft()) {
      return left(eventTypeOrError.value);
    }

    const eventType = eventTypeOrError.value;
    const eventOrError = this.buildEvent(eventType, dto);

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    return right(eventOrError.value);
  }

  private static buildEvent(
    eventType: EventType,
    dto: TrackingEventDto,
  ): Either<InvalidArgumentError | UnexpectedError | MoneyError, EventTracking> {
    if (eventType.isViewContent()) {
      const eventDto: ViewContentPrimitives = {
        name: dto.name,
        id: dto.id ?? UniqueEntityID.create().toPrimitive(),
        externalId: dto.externalId,
        organizationId: dto.organizationId,
        urlPage: dto.urlPage,
        user: dto.user,
        fb: dto.fb,
        content: dto.content.get() as ViewContentContentPrimitives,
        remoteAddress: dto.remoteAddress,
        userAgent: dto.userAgent,
        eventId: dto.eventId,
        sessionId: dto.sessionId,
        serviceType: dto.serviceType,
        containerType: dto.containerType,
      };

      return ViewContent.build(eventDto);
    }

    if (eventType.isAddToCart()) {
      const eventDto: AddToCartPrimitives = {
        name: dto.name,
        id: dto.id ?? UniqueEntityID.create().toPrimitive(),
        externalId: dto.externalId,
        organizationId: dto.organizationId,
        urlPage: dto.urlPage,
        user: dto.user,
        fb: dto.fb,
        content: dto.content.get() as TrackingContentPrimitives,
        price: dto.price.get(),
        numItems: dto.numItems.get(),
        totalPrice: dto.totalPrice.get(),
        items: dto.items.get(),
        remoteAddress: dto.remoteAddress,
        userAgent: dto.userAgent,
        eventId: dto.eventId,
        sessionId: dto.sessionId,
        serviceType: dto.serviceType,
        containerType: dto.containerType,
      };

      return AddToCart.build(eventDto);
    }

    if (eventType.isInitiateCheckout()) {
      const eventDto: InitiateCheckoutPrimitives = {
        name: dto.name,
        id: dto.id ?? UniqueEntityID.create().toPrimitive(),
        externalId: dto.externalId,
        organizationId: dto.organizationId,
        urlPage: dto.urlPage,
        user: dto.user,
        fb: dto.fb,
        content: dto.content.get() as InitiateCheckoutContentPrimitives,
        price: dto.price.get(),
        numItems: dto.numItems.get(),
        totalPrice: dto.totalPrice.get(),
        items: dto.items.get(),
        remoteAddress: dto.remoteAddress,
        userAgent: dto.userAgent,
        eventId: dto.eventId,
        sessionId: dto.sessionId,
        serviceType: dto.serviceType,
        containerType: dto.containerType,
      };

      return InitiateCheckout.build(eventDto);
    }

    if (eventType.isPurchase()) {
      const eventDto: PurchasePrimitives = {
        name: dto.name,
        id: dto.id ?? UniqueEntityID.create().toPrimitive(),
        externalId: dto.externalId,
        organizationId: dto.organizationId,
        urlPage: dto.urlPage,
        user: dto.user,
        fb: dto.fb,
        content: dto.content.get() as PurchaseContentPrimitives,
        price: dto.price.get(),
        numItems: dto.numItems.get(),
        totalPrice: dto.totalPrice.get(),
        items: dto.items.get(),
        remoteAddress: dto.remoteAddress,
        userAgent: dto.userAgent,
        eventId: dto.eventId,
        sessionId: dto.sessionId,
        serviceType: dto.serviceType,
        containerType: dto.containerType,
      };

      return Purchase.build(eventDto);
    }

    const eventDto: PageViewPrimitives = {
      name: dto.name,
      id: dto.id ?? UniqueEntityID.create().toPrimitive(),
      externalId: dto.externalId,
      organizationId: dto.organizationId,
      urlPage: dto.urlPage,
      route: dto.route.get(),
      user: dto.user,
      fb: dto.fb,
      remoteAddress: dto.remoteAddress,
      userAgent: dto.userAgent,
      eventId: dto.eventId,
      sessionId: dto.sessionId,
      serviceType: dto.serviceType,
      containerType: dto.containerType,
    };

    return PageView.build(eventDto);
  }
}
