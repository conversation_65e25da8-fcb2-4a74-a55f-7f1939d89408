import { EEventServices } from '@discocil/fv-domain-library';

import { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import { EEventTypes } from '@/tracking/domain/value-objects/EventType';

export const fvTrackingSchema = {
  _id: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
    enum: Object.values(EEventTypes),
  },
  event_id: {
    type: String,
    required: false,
    index: true,
  },
  organization_id: {
    type: String,
    required: true,
    index: true,
  },
  url_page: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: false,
  },
  remote_address: {
    type: String,
    required: true,
  },
  user_agent: {
    type: String,
    required: true,
  },
  session_id: {
    type: String,
    required: true,
    index: true,
  },
  service_type: {
    type: String,
    required: false,
    enum: Object.values(EEventServices),
    index: true,
  },
  container_type: {
    type: String,
    required: false,
    enum: Object.values(EMicrositeContainerType),
  },
};
