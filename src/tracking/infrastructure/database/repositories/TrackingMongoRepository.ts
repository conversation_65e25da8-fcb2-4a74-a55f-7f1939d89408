import {
  Paginator, type Criteria, type RequiredCriteria,
} from '@discocil/fv-criteria-converter-library/domain';
import { left, right } from '@discocil/fv-domain-library';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { FvTrackingMapper } from '../mappers/FvTrackingMapper';
import { FvTrackingSchemaMapper } from '../mappers/FvTrackingSchemaMapper';
import { fvTrackingSchema } from '../schemas/FvTrackingSchema';

import type { TrackingSaveRepository, TrackingSearchRepository } from '@/tracking/domain/contracts/TrackingMongoRepository';
import type { EventTracking } from '@/tracking/domain/entities/EventTracking';
import type {
  TrackingKeys,
  Trackings,
  TrackingsEither,
} from '@/tracking/domain/services/EventTrackingFactory';
import type { FvTrackingSchemaType } from '../schemas/FvTrackingSchemaType';

export type PropertiesMapper = Partial<Record<TrackingKeys, keyof FvTrackingSchemaType>>;

export class TrackingFvMongoRepository extends MongoRepository implements TrackingSaveRepository, TrackingSearchRepository {
  protected getSchema(): Schema {
    return new Schema(fvTrackingSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'tracking_events';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      sessionId: 'session_id',
      eventId: 'event_id',
      organizationId: 'organization_id',
    };
  }

  async save(tracking: EventTracking): Promise<void> {
    const toSave = FvTrackingSchemaMapper.execute(tracking);

    const filter = { _id: tracking.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async search(criteria: Criteria): Promise<TrackingsEither> {
    const response: Trackings = new Map();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<FvTrackingSchemaType[]>();

    if (queryResponse.length === 0) {
      return right({ trackings: response });
    }

    for (const model of queryResponse) {
      const trackingResult = FvTrackingMapper.execute(model);

      if (trackingResult.isLeft()) {
        return left(trackingResult.value);
      }

      const tracking = trackingResult.value;

      response.set(tracking.id, tracking);
    }

    if (criteria.pagination) {
      const total = await connection.countDocuments(filterQuery.filter);
      const requiredCriteria = criteria as RequiredCriteria;

      return right({
        trackings: response,
        pagination: Paginator.execute(total, requiredCriteria, queryResponse.length),
      });
    }

    return right({ trackings: response });
  }
}
