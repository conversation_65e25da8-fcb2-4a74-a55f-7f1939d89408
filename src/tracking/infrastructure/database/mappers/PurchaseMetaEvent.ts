import type { Purchase } from '@/tracking/domain/entities/Purchase';
import type { MetaEvent, MetaEventStrategy } from './MetaEvent';

export class PurchaseMetaEvent implements MetaEventStrategy {
  execute(metaEvent: MetaEvent): void {
    const event = metaEvent.event as Purchase;

    metaEvent.setContentIds(event.content.ids);
    metaEvent.setValue(event.totalPrice.amount);
    metaEvent.setCurrency(event.price.currency.toUpperCase());
    metaEvent.setNumItems(event.numItems);

    metaEvent.addContent({
      items: event.items,
      id: event.content.id,
      title: event.content.name,
      itemPrice: event.price.amount,
      category: event.content.type,
    });
  }
}
