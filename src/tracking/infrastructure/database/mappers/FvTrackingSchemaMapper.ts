

import type { EventTracking } from '@/tracking/domain/entities/EventTracking';
import type { FvTrackingSchemaType } from '../schemas/FvTrackingSchemaType';

export class FvTrackingSchemaMapper {
  static execute(data: EventTracking): FvTrackingSchemaType {
    return {
      _id: data.id,
      name: data.name,
      event_id: data.eventId.get(),
      organization_id: data.organizationId,
      url_page: data.urlPage,
      price: null,
      remote_address: data.remoteAddress,
      user_agent: data.userAgent,
      session_id: data.sessionId.get(),
      service_type: data.serviceType.get(),
      container_type: data.containerType.get(),
    };
  }
}
