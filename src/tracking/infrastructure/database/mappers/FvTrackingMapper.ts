import { Maybe } from '@discocil/fv-domain-library';

import { EventMetaFactory } from '@/tracking/domain/services/EventTrackingFactory';
import { EEventChannel } from '@/tracking/domain/value-objects/EventType';

import type { TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { TrackingEither } from '@/tracking/domain/services/EventTrackingFactory';
import type { FvTrackingSchemaType } from '../schemas/FvTrackingSchemaType';

export class FvTrackingMapper {
  static execute(data: FvTrackingSchemaType): TrackingEither {
    const dto: TrackingEventDto = {
      id: data._id,
      channel: EEventChannel.fv,
      name: data.name,
      externalId: Maybe.none(),
      organizationId: data.organization_id,
      urlPage: data.url_page,
      route: Maybe.none(),
      user: Maybe.none(),
      fb: Maybe.none(),
      content: Maybe.none(),
      price: Maybe.none(),
      items: Maybe.none(),
      numItems: Maybe.none(),
      totalPrice: Maybe.none(),
      remoteAddress: data.remote_address,
      userAgent: data.user_agent,
      eventId: Maybe.some(data.event_id),
      sessionId: Maybe.some(data.session_id),
      serviceType: Maybe.some(data.service_type),
      containerType: Maybe.some(data.container_type),
    };

    return EventMetaFactory.execute(dto);
  }
}
