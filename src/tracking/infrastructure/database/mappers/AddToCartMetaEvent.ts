import type { AddToCart } from '@/tracking/domain/entities/AddToCart';
import type { MetaEvent, MetaEventStrategy } from './MetaEvent';

export class AddToCartMetaEvent implements MetaEventStrategy {
  execute(metaEvent: MetaEvent): void {
    const event = metaEvent.event as AddToCart;

    metaEvent.setContentIds(event.content.ids);
    metaEvent.setValue(event.totalPrice.amount);
    metaEvent.setCurrency(event.price.currency.toUpperCase());
    metaEvent.setNumItems(event.numItems);

    metaEvent.addContent({
      items: event.items,
      id: event.content.id,
      title: event.content.name,
      itemPrice: event.price.amount,
      category: event.content.type,
    });
  }
}
