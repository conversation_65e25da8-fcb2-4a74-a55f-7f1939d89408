import {
  Collection,
  CryptoService,
  FvDate,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import {
  Content,
  CustomData,
  EventRequest,
  ServerEvent,
  UserData,
} from 'facebook-nodejs-business-sdk';

import { UnsupportedEventError } from '@/tracking/domain/errors/UnsupportedEventError';

import type { TrackingItemPrimitive } from '@/tracking/domain/contracts/EntityContracts';
import type { Credentials } from '@/tracking/domain/contracts/TrackingRepository';
import type { EventTracking } from '@/tracking/domain/entities/EventTracking';
import type { Either } from '@discocil/fv-domain-library/domain';

enum TrackingConstants {
  ActionSource = 'website',
  ContentType = 'product'
};

type MetaContent = {
  readonly items?: TrackingItemPrimitive[];
  readonly id?: string;
  readonly title: string;
  readonly itemPrice?: number;
  readonly category?: string;
};

export type EitherResponse = Either<UnsupportedEventError, MetaEvent>;

type TrackResponse = {
  readonly id: string;
  readonly fbtraceId: string;
};

type TrackBuildRequest = {
  readonly event: EventTracking;
  readonly credentials: Credentials;
  readonly strategies: MetaEventStrategyType;
};

export interface MetaEventStrategy {
  execute(metaEvent: MetaEvent): void;
}

// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
export type MetaEventStrategyType = WeakMap<Function, () => MetaEventStrategy>;

export class MetaEvent {
  private readonly userData = new UserData();
  private readonly customData = new CustomData();
  private readonly contents = Collection.new<Content>();
  private readonly serverEvent = new ServerEvent();
  private readonly eventRequest: EventRequest;

  private constructor(
    readonly event: EventTracking,
    private readonly credentials: Credentials,
    eventStrategy: (metaEvent: MetaEvent) => void,
  ) {
    this.eventRequest = new EventRequest(this.credentials.accessToken, this.credentials.pixelId);

    eventStrategy(this);

    this.serverEvent.setEventName(this.event.name);
    this.serverEvent.setEventTime(FvDate.create().toSeconds());
    this.serverEvent.setEventSourceUrl(this.event.urlPage);
    this.serverEvent.setActionSource(TrackingConstants.ActionSource);
    this.serverEvent.setEventId(this.event.id);
    this.serverEvent.setUserData(this.userData);

    if (this.event.fb.isDefined()) {
      const fbclid = this.event.fb.get().fbclid;

      if (fbclid.isDefined()) {
        this.setCustomProperties({ fbclid: fbclid.get() });
      }
    }
  }

  static async build(request: TrackBuildRequest): Promise<EitherResponse> {
    const {
      event,
      credentials,
      strategies,
    } = request;

    const strategy = strategies.get(event.constructor);

    if (!strategy) {
      return left(UnsupportedEventError.build(
        {
          context: this.constructor.name,
          data: request,
        },
      ));
    }

    const metaEvent = new MetaEvent(event, credentials, strategy().execute.bind(strategy));

    await metaEvent.buildUserData();

    return right(metaEvent);
  }

  private async buildUserData(): Promise<void> {
    this.userData.setClientIpAddress(this.event.remoteAddress);
    this.userData.setClientUserAgent(this.event.userAgent);

    if (this.event.externalId.isDefined()) {
      this.userData.setExternalId(this.event.externalId.get());
    }

    if (this.event.user.isDefined()) {
      const userData = this.event.user.get();
      const userName = userData.name;
      const userEmail = userData.email;
      const userPhone = userData.phone;

      if (userEmail.isDefined()) {
        const emailHashed = await CryptoService.sha256(userEmail.get().trim());

        this.userData.setEmail(emailHashed);
      }

      if (userPhone.isDefined()) {
        const phoneHashed = await CryptoService.sha256(userPhone.get().trim());

        this.userData.setPhone(phoneHashed);
      }

      if (userName.isDefined()) {
        const { firstName, lastName } = await this.getFirstAndLastName(userName.get());

        this.userData.setFirstName(firstName);

        if (lastName) {
          this.userData.setLastName(lastName);
        }
      }
    }

    if (this.event.fb.isDefined()) {
      const fbData = this.event.fb.get();
      const fbp = fbData.fbp;
      const fbc = fbData.fbc;

      if (fbp.isDefined()) {
        this.userData.setFbp(fbp.get());
      }

      if (fbc.isDefined()) {
        this.userData.setFbc(fbc.get());
      }
    }
  }

  private async getFirstAndLastName(fullname: string): Promise<{ firstName: string; lastName: string | null; }> {
    const nameTrimmed = fullname.trim();
    const indexBlankSpace = nameTrimmed.indexOf(' ');

    if (indexBlankSpace === -1) {
      const firstName = await CryptoService.sha256(nameTrimmed);

      return { firstName, lastName: null };
    }


    const firstName = await CryptoService.sha256(nameTrimmed.slice(0, indexBlankSpace));
    const lastName = await CryptoService.sha256(nameTrimmed.slice(indexBlankSpace + 1));

    return { firstName, lastName };
  }

  async track(): Promise<TrackResponse> {
    if (this.contents.isNotEmpty()) {
      this.customData.setContents(this.contents.toArray());
    }

    this.serverEvent.setCustomData(this.customData);
    this.eventRequest.setEvents([this.serverEvent]);

    const response = await this.eventRequest.execute();

    return {
      id: response.id,
      fbtraceId: response.fbtrace_id,
    };
  }

  addContent(content: MetaContent): this {
    for (const contentItem of (content.items ?? [])) {
      const itemContentData = new Content();

      if (content.id) {
        itemContentData.setId(content.id);
      }

      itemContentData.setQuantity(contentItem.quantity);
      itemContentData.setItemPrice(contentItem.price);
      itemContentData.setTitle(content.title);
      itemContentData.setCategory(contentItem.category);

      this.contents.add(itemContentData);
    }

    return this;
  }

  setCustomProperties(customProperties: Record<string, unknown>): this {
    this.customData.setCustomProperties(customProperties);

    return this;
  }

  setContentIds(value: string[]): this {
    this.customData.setContentIds(value);

    return this;
  }

  setValue(value: number): this {
    this.customData.setValue(value);

    return this;
  }

  setCurrency(value: string): this {
    this.customData.setCurrency(value);

    return this;
  }

  setNumItems(value: number): this {
    this.customData.setNumItems(value);

    return this;
  }

  setContentType(): this {
    this.customData.setContentType(TrackingConstants.ContentType);

    return this;
  }
}
