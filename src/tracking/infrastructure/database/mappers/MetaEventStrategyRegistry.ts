/* eslint-disable @typescript-eslint/no-unsafe-function-type */

import { AddToCart } from '@/tracking/domain/entities/AddToCart';
import { InitiateCheckout } from '@/tracking/domain/entities/InitiateCheckout';
import { PageView } from '@/tracking/domain/entities/PageView';
import { Purchase } from '@/tracking/domain/entities/Purchase';
import { ViewContent } from '@/tracking/domain/entities/ViewContent';

import { AddToCartMetaEvent } from './AddToCartMetaEvent';
import { InitiateCheckoutMetaEvent } from './InitiateCheckoutMetaEvent';
import { PageViewMetaEvent } from './PageViewMetaEvent';
import { PurchaseMetaEvent } from './PurchaseMetaEvent';
import { ViewContentMetaEvent } from './ViewContentMetaEvent';

import type { MetaEventStrategy, MetaEventStrategyType } from './MetaEvent';

export const metaEventStrategies: MetaEventStrategyType = new WeakMap<Function, () => MetaEventStrategy>([
  [PageView, (): MetaEventStrategy => new PageViewMetaEvent()],
  [ViewContent, (): MetaEventStrategy => new ViewContentMetaEvent()],
  [AddToCart, (): MetaEventStrategy => new AddToCartMetaEvent()],
  [InitiateCheckout, (): MetaEventStrategy => new InitiateCheckoutMetaEvent()],
  [Purchase, (): MetaEventStrategy => new PurchaseMetaEvent()],
]);
