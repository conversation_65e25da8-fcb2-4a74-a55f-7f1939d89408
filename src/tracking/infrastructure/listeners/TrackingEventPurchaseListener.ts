import {
  UniqueEntityID,
  type DomainEvent,
  type DomainEventClass,
  type DomainEventSubscriber,
  type Logger,
} from '@discocil/fv-domain-library/domain';

import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';
import { Purchase } from '@/tracking/domain/entities/Purchase';
import { TrackedPurchaseDomainEvent } from '@/tracking/domain/events/TrackedPurchaseDomainEvent';

import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';

export class TrackingEventPurchaseListener implements DomainEventSubscriber<DomainEvent> {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly logger: Logger,
  ) {}

  subscribedTo(): DomainEventClass[] {
    return [TrackedPurchaseDomainEvent];
  }

  async on(domainEvent: DomainEvent): Promise<void> {
    const { attributes } = domainEvent;

    const primitives = attributes as Purchase;
    const purchaseEventOrError = Purchase.build(primitives);

    if (purchaseEventOrError.isLeft()) {
      return;
    }

    const purchaseEvent = purchaseEventOrError.value;

    const criteria = TicketCriteriaMother.purchaseIdToMatch(
      UniqueEntityID.build(purchaseEvent.id),
      UniqueEntityID.build(purchaseEvent.organizationId),
    );

    const ticketsOrError = await this.ticketRepository.search(criteria);

    if (ticketsOrError.isLeft()) {
      return;
    }

    const tickets = ticketsOrError.value.tickets;

    for (const ticket of tickets.values()) {
      ticket.shouldNotEmitPurchaseEvent();
    }

    await this.ticketRepository.saveMany(tickets);

    this.logger.info('Tracking Event Purchase Listener');
  }
}
