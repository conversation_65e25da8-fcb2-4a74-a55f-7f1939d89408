import { PerformanceMeasurementDecorator } from '@discocil/fv-domain-library/application';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { EventDependencyIdentifier } from '@/events/events/domain/dependencyIdentifier/EventDependencyIdentifier';
import { MicrositeDependencyIdentifier } from '@/microsite/domain/dependencyIdentifier/MicrositeDependencyIdentifier';
import { TrackingEventUseCase } from '@/tracking/application/TrackingEventUseCase';
import { TrackingFvEventUseCase } from '@/tracking/application/TrackingFvEventUseCase';

import { TrackingMetaRepository } from '../database/repositories/TrackingMetaRepository';
import { TrackingFvMongoRepository } from '../database/repositories/TrackingMongoRepository';

import type { InternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { MicrositeRepository } from '@/microsite/domain/contracts/MicrositeRepository';
import type { Logger } from '@discocil/fv-domain-library/domain';
import type { DependencyContainer } from 'tsyringe';

export const TrackingContainer = {
  register: (): void => {
    container.register(TrackingEventUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new TrackingEventUseCase(
          new TrackingMetaRepository(),
          container.resolve<MicrositeRepository>(MicrositeDependencyIdentifier.MicrositeRepository),
          container.resolve<InternalMessageBrokerClient>(DependencyIdentifier.InternalMessageBrokerClient),
        );

        return new PerformanceMeasurementDecorator(
          TrackingEventUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(TrackingFvEventUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        const useCase = new TrackingFvEventUseCase(
          new TrackingFvMongoRepository(dbConnection),
          container.resolve<EventRepository>(EventDependencyIdentifier.EventRepository),
        );

        return new PerformanceMeasurementDecorator(
          TrackingFvEventUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });
  },
};
