import { env, FIVE_MINUTES_IN_SECONDS } from './Configuration';
import { AplicationIds, Enviroments } from './enviroments';

import type { Configuration } from './Configuration';

export const configuration: Partial<Configuration> = {
  apikey: env.APP_KEY,
  env: Enviroments.ALPHA,
  fv: {
    apps: {
      clients: {
        applicationId: AplicationIds.CLIENTS,
        url: 'https://clients-alpha.fourvenues.com',
      },
      professionals: { applicationId: AplicationIds.PROFESSIONALS },
      microsites: {
        applicationId: AplicationIds.CLIENTS,
        url: 'https://cli-alpha.fourvenues.com',
      },
    },
    services: {
      api: { url: 'https://api-alpha.fourvenues.com' },
      connector: { url: 'https://connector-service-alpha.fourvenues.com' },
      payment: { url: 'https://alpha.pay.fourvenues.com' },
      reservations: {
        apiKey: env.FV_SERVICES_RESERVATIONS_API_KEY,
        url: 'https://reservations-api-service-alpha.fourvenues.com/api',
      },
      tickets: {
        apiKey: env.FV_SERVICES_TICKETS_API_KEY,
        url: 'https://tickets-alpha.fourvenues.com/api',
      },
    },
  },
  logLevel: 'error',
  origin: [
    'https://cli-alpha.fourvenues.com',
    'https://alpha.fourvenues.com',
    'https://site-alpha.fourvenues.com',
    'https://web-alpha.fourvenues.com',
  ],
  rabbit: {
    user: env.RABBIT_USER,
    password: env.RABBIT_PASSWORD,
    host: env.RABBIT_HOST,
    port: env.RABBIT_PORT,
    env: env.RABBIT_ENV,
    url: env.RABBIT_URL,
  },
  redis: {
    enabled: true,
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    ttl: FIVE_MINUTES_IN_SECONDS,
    database: 7,
  },
};
