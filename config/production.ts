import { env, FIVE_MINUTES_IN_SECONDS } from './Configuration';
import { AplicationIds, Enviroments } from './enviroments';

import type { Configuration } from './Configuration';

export const configuration: Partial<Configuration> = {
  apiDocumentation: false,
  apikey: env.APP_KEY,
  env: Enviroments.PRODUCTION,
  fv: {
    apps: {
      clients: {
        applicationId: AplicationIds.CLIENTS,
        url: 'https://clients.fourvenues.com',
      },
      professionals: { applicationId: AplicationIds.PROFESSIONALS },
      microsites: {
        applicationId: AplicationIds.CLIENTS,
        url: 'https://cli.fourvenues.com',
      },
    },
    services: {
      api: { url: 'https://api.fourvenues.com' },
      connector: { url: 'https://connector-service.fourvenues.com' },
      payment: { url: 'https://pay.fourvenues.com' },
      reservations: {
        apiKey: env.FV_SERVICES_RESERVATIONS_API_KEY,
        url: 'https://reservations-api-service.fourvenues.com/api',
      },
      tickets: {
        apiKey: env.FV_SERVICES_TICKETS_API_KEY,
        url: 'https://tickets.fourvenues.com/api',
      },
    },
  },
  logLevel: 'error',
  origin: [
    'https://cli.fourvenues.com',
    'https://www.fourvenues.com',
    'https://fourvenues.com',
    'https://site.fourvenues.com',
    'https://web.fourvenues.com',
  ],
  rabbit: {
    user: env.RABBIT_USER,
    password: env.RABBIT_PASSWORD,
    host: env.RABBIT_HOST,
    port: env.RABBIT_PORT,
    env: env.RABBIT_ENV,
    url: env.RABBIT_URL,
  },
  redis: {
    enabled: true,
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    ttl: FIVE_MINUTES_IN_SECONDS,
    database: 7,
  },
  localization: {
    localesPath: 'dist/locales',
    defaultLocale: 'en',
    fallbackLocale: 'es',
    supportedLocales: ['es', 'en', 'fr', 'it', 'nl', 'ca', 'pt'],
    namespaces: [
      'common',
    ],
    phraseApiUrl: 'https://api.phrase.com/v2/projects/',
    phraseProjectId: 'adfcbcff78823027d3367d4af5e2eca7',
    phraseApiKey: env.PHRASE_KEY,
  },

};
