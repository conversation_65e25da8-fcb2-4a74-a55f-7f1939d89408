import {
  EEventServices,
  FvDate,
  Maybe,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import supertest from 'supertest';
import { container } from 'tsyringe';

import { BookingTypeMongoRepository } from '@/bookings/bookingTypes/infrastructure/database/repositories/BookingTypeRepository';
import { BookingZoneMongoRepository } from '@/bookings/bookingZones/infrastructure/database/repositories/BookingZoneRepository';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { FindBookingSpaceAvailabilityUseCase } from '@/events/events/application/FindBookingSpaceAvailabilityUseCase';
import { BookingSpaceAvailabilityExpired } from '@/events/events/domain/errors/BookingSpaceAvailabilityExpired';
import { BookingSpaceAvailabilityNotFound } from '@/events/events/domain/errors/BookingSpaceAvailabilityNotFound';
import { BookingSpaceAvailabilityUnauthorized } from '@/events/events/domain/errors/BookingSpaceAvailabilityUnauthorized';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { successResponseSchema } from '@app/http/@types/cli-api/events/findBookingSpaceAvailability/successResponseSchema';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';
import { startServer, type IServer } from '@tests/mocks/Server';
import { BookingTypeMother } from '@tests/stubs/booking/BookingTypeMother';
import { BookingZoneMother } from '@tests/stubs/booking/BookingZoneMother';
import { BookingSpaceAvailabilityRepositoryTest } from '@tests/stubs/event/BookingSpaceAvailabilityRepositoryTest';
import { BookingSpaceAvailabilityMother, EventMother } from '@tests/stubs/event/EventMother';
import { EventRepositoryTest } from '@tests/stubs/event/EventRepositoryTest';
import { LocationRepositoryTest } from '@tests/stubs/locations/LocationRepositoryTest';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { OrganizationRepositoryTest } from '@tests/stubs/organization/OrganizationRepositoryTest';
import { LocationMother } from '@tests/stubs/ticket/LocationMother';
import { UserMother } from '@tests/stubs/user/UserMother';
import { UserRepositoryTest } from '@tests/stubs/user/UserRepositoryTest';

import type { BookingSpacePrimitives, BookingSpacesPrimitives } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { RawServerBase } from 'fastify';

const BASE_URL = '/api/events/reservation-availability';

describe(`E2E | ${FindBookingSpaceAvailabilityUseCase.name}`, () => {
  let httpServer: IServer;
  let server: RawServerBase;

  let dbConnection: DatabaseConnection;
  let organizationRepository: OrganizationRepositoryTest;
  let eventRepository: EventRepositoryTest;
  let userRepository: UserRepositoryTest;
  let locationRepository: LocationRepositoryTest;

  const organization = OrganizationMother.buildDefault();
  const collaborator = UserMother.buildWithCustomData({ organizations: [organization.id] });
  const location = LocationMother.buildDefault();

  const event = EventMother.buildWithCustomData({
    organizationId: organization.id,
    locationId: location.id,
    services: [EEventServices.BOOKED, EEventServices.BOOKINGS],
  });

  const bookingType = BookingTypeMother.buildWithCustomData({
    organizationId: organization.id,
    eventId: Maybe.some(event.id),
  });

  const space = BookingZoneMother.generateSpace();

  space.slugsTypes = [bookingType.slug];

  const spaces: BookingSpacesPrimitives = new Map<string, BookingSpacePrimitives>();

  spaces.set(space.id, space);

  const bookingZone = BookingZoneMother.buildWithCustomData({
    organizationId: organization.id,
    eventId: Maybe.some(event.id),
    slugsTypes: [bookingType.slug],
    spaces,
  });

  const bookingSpaceAvailability = BookingSpaceAvailabilityMother.buildWithCustomData({
    collaboratorId: collaborator.id,
    eventId: event.id,
  });

  const expiredAvailability = BookingSpaceAvailabilityMother.buildWithCustomData({
    collaboratorId: collaborator.id,
    eventId: event.id,
    expiredAt: FvDate.create().subtractMinutes(5).toMilliseconds(),
  });

  beforeAll(async () => {
    httpServer = await startServer();
    server = httpServer.getInstance();

    dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

    organizationRepository = new OrganizationRepositoryTest(dbConnection);
    userRepository = new UserRepositoryTest(dbConnection);
    locationRepository = new LocationRepositoryTest(dbConnection);
    eventRepository = new EventRepositoryTest(dbConnection);

    const bookingZoneRepository = new BookingZoneMongoRepository(dbConnection);

    const bookingTypeRepository = new BookingTypeMongoRepository(dbConnection);

    const bookingSpaceAvailabilityRepository = new BookingSpaceAvailabilityRepositoryTest(dbConnection);

    await Promise.all([
      organizationRepository.save(organization),
      userRepository.save(collaborator),
      locationRepository.save(location),
      bookingTypeRepository.save(bookingType),
      bookingZoneRepository.save(bookingZone),
      eventRepository.save(event),
      bookingSpaceAvailabilityRepository.save(bookingSpaceAvailability),
      bookingSpaceAvailabilityRepository.save(expiredAvailability),
    ]);
  }, 10_000);

  afterAll(async () => {
    const ids = [UniqueEntityID.build(event.id)];
    const criteria = EventCriteriaMother.idsToMatch(ids);

    await eventRepository.removeMany(criteria);
    await httpServer.stop();
  });

  it('should return 409 if booking availability is expired', async () => {
    const { status, body } = await supertest(server)
      .get(BASE_URL)
      .query({ token: expiredAvailability.token })
      .set('Authorization', `Fv ${config.apikey}`);

    const errorException = BookingSpaceAvailabilityExpired.build({ context: FindBookingSpaceAvailabilityUseCase.name });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body.message).toEqual(errorException.message);
  });

  it('should return 409 for unknown token', async () => {
    const { status, body } = await supertest(server)
      .get(BASE_URL)
      .query({ token: 'not-found' })
      .set('Authorization', `Fv ${config.apikey}`);

    const errorException = BookingSpaceAvailabilityNotFound.build({ context: FindBookingSpaceAvailabilityUseCase.name });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body.message).toEqual(errorException.message);
  });

  it('should return 401 if user is not part of organization', async () => {
    const userWithoutOrg = UserMother.buildWithCustomData({ organizations: [] });

    await userRepository.save(userWithoutOrg);

    const unauthAvailability = BookingSpaceAvailabilityMother.buildWithCustomData({
      eventId: event.id,
      collaboratorId: userWithoutOrg.id,
    });

    const bookingSpaceRepo = new BookingSpaceAvailabilityRepositoryTest(dbConnection);

    await bookingSpaceRepo.save(unauthAvailability);

    const { status, body } = await supertest(server)
      .get(BASE_URL)
      .query({ token: unauthAvailability.token })
      .set('Authorization', `Fv ${config.apikey}`);

    const errorException = BookingSpaceAvailabilityUnauthorized.build({ context: FindBookingSpaceAvailabilityUseCase.name });

    expect(status).toBe(HTTP_CODES.UNAUTHORIZED_401);
    expect(body.message).toEqual(errorException.message);
  });

  it('should return 200 for valid token', async () => {
    const { status, body } = await supertest(server)
      .get(BASE_URL)
      .query({ token: bookingSpaceAvailability.token })
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body).toMatchSchema({ data: successResponseSchema });
  });
});
