import {
  FvDate,
  left,
  MoneyError,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { BookingTypeCollection } from '@/bookings/bookingTypes/domain/entities/BookingTypeCollection';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { FindBookingSpaceAvailabilityUseCase } from '@/events/events/application/FindBookingSpaceAvailabilityUseCase';
import { BookingSpaceAvailabilityExpired } from '@/events/events/domain/errors/BookingSpaceAvailabilityExpired';
import { BookingSpaceAvailabilityNotFound } from '@/events/events/domain/errors/BookingSpaceAvailabilityNotFound';
import { BookingSpaceAvailabilityUnauthorized } from '@/events/events/domain/errors/BookingSpaceAvailabilityUnauthorized';
import { EventMother } from '@tests/stubs/event/EventMother';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { UserMother } from '@tests/stubs/user/UserMother';

import type { FindBookingSpaceAvailabilityDto } from '@/events/events/domain/contracts/bookingSpaceAvailability/FindBookingSpaceAvailabilityContract';
import type { IGetBookingZonesServices } from '@/events/events/domain/contracts/bookingZones/GetBookingZonesContracts';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { AssignBookingRatesToSpacesService } from '@/events/events/domain/services/bookingSpaceAvailability/AssignBookingRatesToSpacesService';
import type { IsEventOwnedByOrganizationService } from '@/events/events/domain/services/bookingSpaceAvailability/IsEventOwnedByOrganizationService';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UserRepository } from '@/user/domain/contracts/UserRepository';

describe(`${FindBookingSpaceAvailabilityUseCase.name}`, () => {
  const eventRepository = mock<EventRepository>();
  const userRepository = mock<UserRepository>();
  const organizationRepository = mock<OrganizationRepository>();
  const assignRatesService = mock<AssignBookingRatesToSpacesService>();
  const isEventOwnedService = mock<IsEventOwnedByOrganizationService>();
  const getZonesService = mock<IGetBookingZonesServices>();

  const useCase = new FindBookingSpaceAvailabilityUseCase(
    userRepository,
    eventRepository,
    organizationRepository,
    isEventOwnedService,
    assignRatesService,
    getZonesService,
  );

  const dto: FindBookingSpaceAvailabilityDto = { token: 'token-123' };

  afterEach(() => jest.clearAllMocks());

  it('should return NotFoundError if event is not found', async () => {
    eventRepository.find.mockResolvedValue(left(NotFoundError.build({ context: 'event', target: 'Event' })));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(NotFoundError);
  });

  it('should return NotFoundError if organization is not found', async () => {
    const event = EventMother.buildWithBookingSpaceAvailability();

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(left(NotFoundError.build({ context: 'organization', target: 'Organization' })));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(NotFoundError);
  });

  it('should return BookingSpaceAvailabilityNotFound if event has no availability', async () => {
    const event = EventMother.buildWithouthBooking();
    const organization = OrganizationMother.buildDefault();

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(right(organization));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(BookingSpaceAvailabilityNotFound);
  });

  it('should return BookingSpaceAvailabilityExpired if availability is expired', async () => {
    const event = EventMother.buildDefault();
    const expiredAvailability = EventMother.buildBookingSpaceAvailabilityCustom(
      { expiredAt: FvDate.create().subtractMinutes(10).toMilliseconds() },
    );

    event.setBookingSpaceAvailability(expiredAvailability);

    const organization = OrganizationMother.buildDefault();

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(right(organization));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(BookingSpaceAvailabilityExpired);
  });

  it('should return NotFoundError if user is not found', async () => {
    const event = EventMother.buildWithBookingSpaceAvailability();
    const organization = OrganizationMother.buildDefault();

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(right(organization));
    userRepository.find.mockResolvedValue(left(NotFoundError.build({ context: 'user', target: 'User' })));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(NotFoundError);
  });

  it('should return BookingSpaceAvailabilityUnauthorized if user is not owner', async () => {
    const event = EventMother.buildWithBookingSpaceAvailability();
    const organization = OrganizationMother.buildDefault();
    const user = UserMother.buildWithCustomData({ organizations: [] });

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(right(organization));
    userRepository.find.mockResolvedValue(right(user));
    isEventOwnedService.execute.mockResolvedValue(
      left(
        BookingSpaceAvailabilityUnauthorized.build({ context: 'FindBookingSpaceAvailabilityUseCase' }),
      ),
    );

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(BookingSpaceAvailabilityUnauthorized);
  });

  it('should return MapperError if get zones service fails', async () => {
    const event = EventMother.buildWithBookingSpaceAvailability();
    const organization = OrganizationMother.buildDefault();
    const user = UserMother.buildWithCustomData({ organizations: [organization.id] });

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(right(organization));
    userRepository.find.mockResolvedValue(right(user));
    isEventOwnedService.execute.mockResolvedValue(right(true));
    getZonesService.execute.mockResolvedValue(left(MapperError.build({ context: 'zones' })));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(MapperError);
  });

  it('should return MoneyError if assign rates service fails', async () => {
    const event = EventMother.buildWithBookingSpaceAvailability();
    const organization = OrganizationMother.buildDefault();
    const user = UserMother.buildWithCustomData({ organizations: [organization.id] });

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(right(organization));
    userRepository.find.mockResolvedValue(right(user));
    isEventOwnedService.execute.mockResolvedValue(right(true));
    getZonesService.execute.mockResolvedValue(
      right({
        event,
        zones: new Map(),
        typesByZones: new BookingTypeCollection(),
      }),
    );
    assignRatesService.execute.mockResolvedValue(left(MoneyError.build({ context: 'assignRates' })));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isLeft()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toBeInstanceOf(MoneyError);
  });

  it('should return EventAvailability on success', async () => {
    const event = EventMother.buildWithBookingSpaceAvailability();
    const organization = OrganizationMother.buildDefault();
    const user = UserMother.buildWithCustomData({ organizations: [organization.id] });

    const zones = new Map();
    const typesByZones = new BookingTypeCollection();
    const zonesSpacesPrices = new Map();

    eventRepository.find.mockResolvedValue(right(event));
    organizationRepository.find.mockResolvedValue(right(organization));
    userRepository.find.mockResolvedValue(right(user));
    isEventOwnedService.execute.mockResolvedValue(right(true));
    getZonesService.execute.mockResolvedValue(
      right({
        event, zones, typesByZones,
      }),
    );
    assignRatesService.execute.mockResolvedValue(right(zonesSpacesPrices));

    const bookingSpaceAvailability = await useCase.execute(dto);

    expect(bookingSpaceAvailability.isRight()).toBeTruthy();
    expect(bookingSpaceAvailability.value).toStrictEqual({
      event,
      user,
      zones,
      zonesSpacesPrices,
    });
  });
});
