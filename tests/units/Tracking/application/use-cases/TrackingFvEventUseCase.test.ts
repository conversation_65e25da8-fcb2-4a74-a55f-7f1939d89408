import {
  ECurrency,
  EMicrositeServices,
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { TrackingFvEventUseCase } from '@/tracking/application/TrackingFvEventUseCase';
import { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import { EEventChannel, EEventTypes } from '@/tracking/domain/value-objects/EventType';
import { EventMother } from '@tests/stubs/event/EventMother';
import { MotherCreator } from '@tests/stubs/MotherCreator';

import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { TrackingMongoRepository } from '@/tracking/domain/contracts/TrackingMongoRepository';

describe(`${TrackingFvEventUseCase.name}`, () => {
  const eventRepository = mock<EventRepository>();
  const trackingMongoRepository = mock<TrackingMongoRepository>();

  const useCase = new TrackingFvEventUseCase(
    trackingMongoRepository,
    eventRepository,
  );

  const event = EventMother.buildDefault();

  const price = MotherCreator.random().number.int({ min: 1, max: 100 });

  const trackingEventDto: TrackingEventDto = {
    channel: EEventChannel.fv,
    name: EEventTypes.AddToCart,
    id: UniqueEntityID.create().toPrimitive(),
    externalId: Maybe.none(),
    organizationId: event.organizationId,
    urlPage: 'https://fv.com/event',
    route: Maybe.none(),
    user: Maybe.none(),
    fb: Maybe.none(),
    content: Maybe.some(
      {
        id: '',
        type: '',
        name: '',
        ids: [
        ],
        date: '1970-01-01T00:00:00.000Z',
      },
    ),
    price: Maybe.some({
      amount: price,
      currency: ECurrency.EUR,
    }),
    items: Maybe.some([]),
    numItems: Maybe.some(0),
    totalPrice: Maybe.some({
      amount: price,
      currency: ECurrency.EUR,
    }),
    remoteAddress: '127.0.0.1',
    eventId: Maybe.some(event.id),
    sessionId: Maybe.some(MotherCreator.random().lorem.slug(5)),
    serviceType: Maybe.some(EMicrositeServices.TICKETS),
    containerType: Maybe.some(EMicrositeContainerType.WEB),
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  };

  it('should return success', async () => {
    eventRepository.find.mockResolvedValue(right(event));


    const useCaseResult = await useCase.execute(trackingEventDto);

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    const response = useCaseResult.value;

    expect(response).toBe(true);
  });

  it('should fail because eventId is empty', async () => {
    const failingTrackingEventDto: TrackingEventDto = {
      ...trackingEventDto,
      eventId: Maybe.none(),
    };

    eventRepository.find.mockResolvedValue(left(NotFoundError.build({
      context: TrackingFvEventUseCase.name,
      target: EventEntity.name,
    })));

    const useCaseResult = await useCase.execute(failingTrackingEventDto);

    expect(useCaseResult.isLeft()).toBeTruthy();

    const response = useCaseResult.value;

    expect(response).toBeInstanceOf(NotFoundError);
  });

  it('should fail because eventId has no matching event', async () => {
    const failingTrackingEventDto = {
      ...trackingEventDto,
      eventId: Maybe.some(UniqueEntityID.create().toPrimitive()),
    };

    eventRepository.find.mockResolvedValue(left(NotFoundError.build({
      context: TrackingFvEventUseCase.name,
      target: EventEntity.name,
    })));

    const useCaseResult = await useCase.execute(failingTrackingEventDto);

    expect(useCaseResult.isLeft()).toBeTruthy();

    const response = useCaseResult.value;

    expect(response).toBeInstanceOf(NotFoundError);
  });
});
