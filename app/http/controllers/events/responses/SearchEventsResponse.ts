import { FvDate } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import type { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import type { Artists } from '@/events/artists/domain/entities/Artist';
import type { EventEntity, SearchPaginatedEvents } from '@/events/events/domain/entities/EventEntity';
import type { LocationEntity } from '@/locations/domain/entities/LocationEntity';
import type { Organization, Organizations } from '@/organizations/organizations/domain/entities/Organization';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/searchEvents/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

type EventsResponse = Response['data'];
type EventResponse = EventsResponse[number];
type OrganizationResponse = EventResponse['organization'];
type OrganizerResponse = EventResponse['organizer'];
type EventDateResponse = EventResponse['dates'];
type EventArtistsResponse = EventResponse['artists'];
type LocationResponse = EventResponse['location'];

export const SearchEventsResponse = (): IResponse<SearchPaginatedEvents, Response> => {
  const execute = (eventsResponse: SearchPaginatedEvents): EventsResponse => {
    const {
      events, billingAddresses, organizations, artists,
    } = eventsResponse;

    const makeOrganization = (organizationId: IdPrimitive, organizations: Organizations): OrganizationResponse => {
      const organization = organizations.get(organizationId) as Organization;

      return {
        id: organization.id,
        slug: organization.slug.fold(() => null, item => item),
        image: organization.image.fold(() => null, item => item),
        cover: organization.cover.fold(() => null, item => item),
      };
    };

    const makeOrganizer = (billingAddress: BillingAddress): OrganizerResponse => {
      return {
        name: billingAddress.companyName,
        cif: billingAddress.cif.fold(() => null, item => item),
        address: billingAddress.address,
        postalCode: billingAddress.postalCode,
        city: billingAddress.municipality.fold(() => null, item => item),
        province: billingAddress.province,
      };
    };

    const makeDates = (event: EventEntity): EventDateResponse => {
      return {
        canceled: event.canceled.fold(() => null, date => FvDate.create(date).toSeconds()),
        date: FvDate.create(event.date).toSeconds(),
        end: FvDate.create(event.endDate).toSeconds(),
        start: FvDate.create(event.startDate).toSeconds(),
      };
    };

    const makeArtists = (artistsIds: string[], artists: Artists): EventArtistsResponse => {
      const artistsResponse: EventArtistsResponse = [];

      for (const artistId of artistsIds) {
        const artist = artists.get(artistId);

        if (!artist) {
          continue;
        }

        artistsResponse.push({
          name: artist.name,
          image: artist.image,
        });
      }

      return artistsResponse;
    };

    const makeLocation = (location: LocationEntity): LocationResponse => {
      return {
        timezone: {
          id: location.timezone.id,
          name: location.timezone.name,
          dstOffset: location.timezone.dstOffset.fold(() => null, item => item),
          rawOffset: location.timezone.rawOffset.fold(() => null, item => item),
        },
      };
    };

    return [...events.values()].map((event) => {
      const billingAddress = billingAddresses.get(event.organizationId);

      return {
        id: event.id,
        organization: makeOrganization(event.organizationId, organizations),
        name: event.name,
        dates: makeDates(event),
        organizer: billingAddress ? makeOrganizer(billingAddress) : null,
        slug: event.slug,
        code: event.code,
        image: event.image.fold(() => null, item => item),
        genres: event.musicalGenres,
        atmosphere: event.atmosphere,
        age: event.age,
        description: event.description.fold(() => null, item => item),
        artists: makeArtists(event.artists, artists),
        location: event.getLocation().fold(() => null, location => makeLocation(location)),
        updatedAt: FvDate.createFromMilliSeconds(event.updatedAt).toMilliseconds(),
      };
    });
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
