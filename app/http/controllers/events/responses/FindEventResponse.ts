import { FvDate } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '@app/http/HttpCodes';

import type { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import type { Artists } from '@/events/artists/domain/entities/Artist';
import type { EventEntity, FindEventResponse as FindEventRequest } from '@/events/events/domain/entities/EventEntity';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/find/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;
type OrganizerResponse = Response['organizer'];

export const FindEventResponse = (): IResponse<FindEventRequest, Response> => {
  const makeOrganizer = (billingAddress: BillingAddress): OrganizerResponse => {
    return {
      name: billingAddress.companyName,
      cif: billingAddress.cif.fold(() => null, item => item),
      address: billingAddress.address,
      postalCode: billingAddress.postalCode,
      city: billingAddress.municipality.fold(() => null, item => item),
      province: billingAddress.province,
    };
  };

  const execute = (dto: FindEventRequest): Response => {
    const {
      event, artists, images, billingAddress, organization,
    } = dto;

    const eventConfiguration = event.getConfiguration();

    return {
      id: event.id,
      name: event.name,
      code: event.code,
      organization: {
        id: organization.id,
        name: organization.name,
        slug: organization.slug.fold(() => null, item => item),
        image: organization.image.fold(() => null, item => item),
        cover: organization.cover.fold(() => null, item => item),
        floorImage: organization.floorImage.fold(() => null, item => item),
      },
      organizer: billingAddress ? makeOrganizer(billingAddress) : null,
      slug: event.slug,
      description: event.description.fold(() => null, item => item),
      location: event.shouldLocationBeShown() ? buildLocation(event) : null,
      imagePlan: event.showImagePlan ? event.imagePlan.fold(() => null, item => item) : null,
      age: event.age,
      visible: event.isVisible,
      perch: event.perch,
      qrMenuId: event.qrMenuId.fold(() => null, item => item),
      image: event.image.fold(() => null, item => item),
      tzName: event.tzName.fold(() => null, item => item),
      isQualityRequired: eventConfiguration.isQualityRequired,
      isReceptionSingen: eventConfiguration.isReceptionSingen,
      isSittingEnabled: eventConfiguration.isSittingEnabled,
      organizerTerms: eventConfiguration.organizerTerms.fold(() => null, item => item),
      rrppCanCancel: eventConfiguration.rrppCanCancel,
      hasEmailReconfirmation: eventConfiguration.hasEmailReconfirmation,
      areFeesShownUpfront: eventConfiguration.areFeesShownUpfront,
      currency: event.currency,
      dates: buildDates(event),
      musicalGenres: event.musicalGenres,
      services: event.services,
      atmosphere: event.atmosphere,
      artists: buildArtists(artists),
      images: images.fold(
        () => null,
        (_images) => {
          return {
            medium: _images.medium.fold(() => null, item => item),
            small: _images.small.fold(() => null, item => item),
          };
        },
      ),
      parentalAuthorization: event.parentalAuthorization.fold(() => null, item => item),
      isPrivate: event.isPrivate,
      preregister: { isActive: event.isPreregisterActive() },
    };
  };

  const buildArtists = (artists: Artists): Response['artists'] => {
    return [...artists.values()].map((artist) => {
      return {
        name: artist.name,
        image: artist.image,
      };
    });
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};

const buildLocation = (event: EventEntity): Response['location'] => {
  const location = event.getLocation().get();

  return {
    province: event.province.isDefined() ? event.province.get() : location.province.fold(() => null, item => item),
    address: location.address.fold(() => null, item => item),
    addressComplete: location.addressComplete.fold(() => null, item => item),
    alias: event.getLocationAlias().fold(() => null, item => item),
    number: location.number.fold(() => null, item => item),
    postalCode: location.postalCode.fold(() => null, item => item),
    municipality: location.municipality.fold(() => null, item => item),
    country: location.country.fold(() => null, item => item),
    coordinates: {
      latitude: location.coordinates.latitude,
      longitude: location.coordinates.longitude,
    },
    timezone: {
      id: location.timezone.id,
      name: location.timezone.name,
      dstOffset: location.timezone.dstOffset.fold(() => null, item => item),
      rawOffset: location.timezone.rawOffset.fold(() => null, item => item),
    },
  };
};

const buildDates = (event: EventEntity): Response['dates'] => {
  return {
    canceled: event.canceled.fold(() => null, date => FvDate.create(date).toSeconds()),
    date: FvDate.create(event.date).toSeconds(),
    end: FvDate.create(event.endDate).toSeconds(),
    limitSale: FvDate.create(event.dateLimitSale).toSeconds(),
    start: FvDate.create(event.startDate).toSeconds(),
  };
};
