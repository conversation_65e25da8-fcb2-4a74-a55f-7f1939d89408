import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchEventsForSitemapUseCase } from '@/events/events/application/SearchEventsForSitemapUseCase';
import { SearchEventsUseCase } from '@/events/events/application/SearchEventsUseCase';

import { parseSearchEventsRequest } from '../requests/ParseSearchEventsRequest';
import { SearchEventsResponse } from '../responses/SearchEventsResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchEventSchema } from '@app/http/@types/cli-api/events/searchEvents/schema';

type Response = ReturnType<ReturnType<typeof SearchEventsResponse>['execute']>;
type Schema = typeof searchEventSchema;

@singleton()
export class SearchEventsController {
  constructor(
    private readonly useCase: SearchEventsUseCase,
    private readonly useCaseForSitemap: SearchEventsForSitemapUseCase,
  ) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = parseSearchEventsRequest(request);

    const useCase = dto.isSitemap
      ? this.useCaseForSitemap
      : this.useCase;

    const useCaseResult = await useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return reply.code(code).send({ message });
    }

    const response = SearchEventsResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
