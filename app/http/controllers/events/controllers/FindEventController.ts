import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindEventUseCase } from '@/events/events/application/FindEventUseCase';

import { parseFindEventRequest } from '../requests/ParseFindEventRequest';
import { FindEventResponse } from '../responses/FindEventResponse';

import type {
  FindEventReply,
  FindEventRequest,
  FindEventResponse as Response,
} from '@app/http/@types/cli-api/events/find/schema';

@singleton()
export class FindEventController {
  constructor(private readonly useCase: FindEventUseCase) { }

  @cacheController()
  async handler(request: FindEventRequest, reply: FindEventReply): Promise<Response> {
    const dto = parseFindEventRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return reply.code(code).send({ message });
    }

    const response = FindEventResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
