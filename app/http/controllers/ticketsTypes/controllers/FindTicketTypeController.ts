import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindTicketTypeUseCase } from '@/tickets/ticketsTypes/application/use-cases/FindTicketTypeUseCase';

import { parseFindTicketTypeRequest } from '../requests/FindTicketTypeRequest';
import { FindTicketTypeResponse } from '../responses/FindTicketTypeResponse';

import type {
  FindTicketTypeReply,
  FindTicketTypeReplyResponse,
  FindTicketTypeRequest,
} from '@app/http/@types/cli-api/ticketsTypes/find/schema';

@singleton()
export class FindTicketTypeController {
  constructor(private readonly useCase: FindTicketTypeUseCase) {}

  @cacheController()
  async handler(request: FindTicketTypeRequest, reply: FindTicketTypeReply): Promise<FindTicketTypeReplyResponse> {
    const dto = parseFindTicketTypeRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return await reply.code(code).send({ message });
    }

    const response = FindTicketTypeResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
