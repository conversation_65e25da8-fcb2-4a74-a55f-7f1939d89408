import { HTTP_CODES } from '@app/http/HttpCodes';

import type { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { TicketTypeOption } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { successResponseSchema } from '@app/http/@types/cli-api/ticketsTypes/find/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

export type Response = Static<typeof successResponseSchema>;

export const FindTicketTypeResponse = (): IResponse<TicketType, Response> => {
  const execute = (dto: TicketType): Response => {
    const ticketType = dto;
    const additionalData = ticketType.additionalData.get();

    return {
      id: ticketType.id,
      name: ticketType.name,
      organizationId: ticketType.organizationId,
      eventId: ticketType.eventId,
      slug: ticketType.slug,
      type: ticketType.type,
      summary: ticketType.summary,
      options: ticketType.options.map((item: TicketTypeOption) => {
        return {
          id: item.id,
          price: item.price,
          name: item.name.fold(() => null, value => value),
          ggddType: item.ggddType,
          ggddAmount: item.ggddAmount,
          age: item.age,
          content: item.content.fold(() => null, value => value),
          additionalInfo: item.additionalInfo.fold(() => null, value => value),
          to: item.getToDateInSeconds(),
          max: item.max,
          image: item.image.fold(() => null, value => value),
        };
      }),
      questions: ticketType.questions,
      supplements: ticketType.supplements.map((item) => {
        const purchaseLimit = item.purchaseLimit.fold(
          () => null,
          purchaseLimit => ({
            minQuantity: purchaseLimit.minQuantity,
            maxQuantity: purchaseLimit.maxQuantity.fold(
              () => null,
              maxQuantity => maxQuantity,
            ),
            isUnlimited: purchaseLimit.isUnlimited,
          }),
        );

        const redemptionDeadline = item.redemptionDeadline.fold(
          () => null,
          redemptionDeadline => redemptionDeadline,
        );

        return {
          id: item.id,
          label: item.label,
          price: item.price,
          fakePrice: item.fakePrice ?? null,
          purchaseLimit,
          redemptionDeadline,
          order: item.order,
        };
      }),
      fields: ticketType.fields,
      warranty: ticketType.warranty,
      customers: {
        max: additionalData.quantitySelector.maximum,
        min: additionalData.quantitySelector.minimum,
        quantity: additionalData.quantitySelector.quantity,
      },
      nominative: ticketType.isNominative,
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
