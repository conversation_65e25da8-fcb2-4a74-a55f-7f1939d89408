import { singleton } from 'tsyringe';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { PurchasePassUseCase } from '@/passes/passes/application/PurchasePassUseCase';

import { parsePurchasePassGuestListsRequest } from './requests/PurchasePassRequest';
import { PurchasePassResponse } from './responses/PurchasePassResponse';

import type { ResponsePurchaseSchemaDto } from '@/passes/passes/domain/contracts/PurchasePassResponse';
import type { PurchasePassReply, PurchasePassRequest } from '@app/http/@types/cli-api/pass/purchase/schema';

@singleton()
export class PurchasePassController {
  constructor(private readonly useCase: PurchasePassUseCase) {}

  async handler(request: PurchasePassRequest, reply: PurchasePassReply): Promise<ResponsePurchaseSchemaDto> {
    const dto = parsePurchasePassGuestListsRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value
        .setCause(EErrorKeys.INVALID_FIELD)
        .contextualize({
          context: this.constructor.name,
          data: { dto },
        });

      const { code, message } = getErrorInfo(request.error);

      return reply.code(code).send({ message });
    }

    const response = PurchasePassResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
