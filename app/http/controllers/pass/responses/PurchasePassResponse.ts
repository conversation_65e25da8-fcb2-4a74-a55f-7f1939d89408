import { FvDate } from '@discocil/fv-domain-library/domain';

import { UrlsPayment } from '@/payments/infrastructure/decorators/UrlsPayment';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { ResponsePurchaseSchemaDto } from '@/passes/passes/domain/contracts/PurchasePassResponse';
import type { Pass, Passes } from '@/passes/passes/domain/entities/Pass';
import type { purchasePassSuccessResponseSchema } from '@app/http/@types/cli-api/pass/purchase/successResponseSchema';
import type { Static } from '@sinclair/typebox';

interface IPurchasePassResponse {
  execute: (purchase: ResponsePurchaseSchemaDto) => PurchasePassResponseType;
  status: () => HTTP_CODES;
}

type PurchasePassResponseType = Static<typeof purchasePassSuccessResponseSchema>;

type PassPurchased = PurchasePassResponseType['inserted'][number];

export const PurchasePassResponse = (): IPurchasePassResponse => {
  const execute = (purchase: ResponsePurchaseSchemaDto): PurchasePassResponseType => {
    const urls = purchase.payment ? new UrlsPayment(purchase.payment) : null;

    return {
      inserted: makePasses(purchase.passes),
      ids: [...purchase.passes.values()].map((pass: Pass) => pass.id),
      needPayment: !!purchase.payment,
      payment_url: urls?.getUrl() ?? null,
    };
  };

  const makePasses = (passes: Passes): PurchasePassResponseType['inserted'] => {
    return [...passes.values()].map((pass: Pass) => {
      const customer = pass.customer;

      const customerInserted: PassPurchased['customer'] = {
        fullname: customer.fullname,
        email: customer.email,
        phone: customer.phone.fold(() => null, item => item),
        address: customer.address.fold(() => null, item => item),
        birthday: customer.birthday.fold(() => null, item => FvDate.create(item).toSeconds()),
        country: customer.country.fold(() => null, item => item),
        postalCode: customer.postalCode.fold(() => null, item => item),
        gender: customer.gender.fold(() => null, item => item),
        personalDocumentNumber: customer.personalDocumentNumber.fold(() => null, item => item.number),
        personalDocumentType: customer.personalDocumentNumber.fold(() => null, item => item.type),
        image: customer.image.fold(() => null, item => item),
        customer_custom_fields1: customer.customerCustomFields1.fold(() => null, item => item),
        customer_custom_fields2: customer.customerCustomFields2.fold(() => null, item => item),
      };

      return {
        id: pass.id,
        organizationId: pass.organizationId,
        applicationId: pass.applicationId,
        organizationAssignedId: pass.organizationAssignedId.fold(() => null, item => item),
        customer: customerInserted,
        typeId: pass.typeId.fold(() => null, item => item),
        typeName: pass.typeName.fold(() => null, item => item),
        typeColor: pass.typeColor.fold(() => null, item => item),
        priceId: pass.priceId.fold(() => null, item => item),
        purchaseDate: pass.getPurchaseDateInSeconds(),
        purchasePrice: pass.purchasePrice,
        serviceFees: pass.serviceFees,
        revenue: pass.revenue,
        paymentId: pass.paymentId.fold(() => null, item => item),
        state: pass.state,
        idx: pass.idx,
        saleType: pass.saleType,
        language: pass.language,
        browser: pass.device.browser,
        device: pass.device.device,
        os: pass.device.os,
      };
    });
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.CREATED_201,
  };
};
