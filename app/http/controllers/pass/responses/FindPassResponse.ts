import { FvDate } from '@discocil/fv-domain-library/domain';

import { UrlsPass } from '@/passes/passes/infrastructure/decorators/UrlsPass';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Pass } from '@/passes/passes/domain/entities/Pass';
import type { successResponseSchema } from '@app/http/@types/cli-api/pass/find/response';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

export type Response = Static<typeof successResponseSchema>;

export const FindPassResponse = (): IResponse<Pass, Response> => {
  const execute = (pass: Pass): Response => {
    const customer = {
      fullname: pass.customer.fullname,
      email: pass.customer.email,
      postalCode: pass.customer.postalCode.fold(() => null, item => item),
      personalDocumentNumber: pass.customer.personalDocumentNumber.fold(() => null, item => item.number),
      personalDocumentType: pass.customer.personalDocumentNumber.fold(() => null, item => item.type),
      birthday: pass.customer.birthday.fold(() => null, item => FvDate.create(item).toSeconds()),
      image: pass.customer.image.fold(() => null, item => item),
      phone: pass.customer.phone.fold(() => null, item => item),
      gender: pass.customer.gender.fold(() => null, item => item),
      address: pass.customer.address.fold(() => null, item => item),
      country: pass.customer.country.fold(() => null, item => item),
      customerCustomFields1: pass.customer.customerCustomFields1.fold(() => null, item => item),
      customerCustomFields2: pass.customer.customerCustomFields2.fold(() => null, item => item),
    };

    const urls = new UrlsPass(pass);

    return {
      id: pass.id,
      organizationId: pass.organizationId,
      applicationId: pass.applicationId,
      organizationAssignedId: pass.organizationAssignedId.fold(() => null, item => item),
      code: pass.code,
      sourceUrl: pass.sourceUrl.fold(() => null, item => item),
      customer,
      typeId: pass.typeId.fold(() => null, item => item),
      typeName: pass.typeName.fold(() => null, item => item),
      typeColor: pass.typeColor.fold(() => null, item => item),
      priceId: pass.priceId.fold(() => null, item => item),
      purchaseDate: pass.getPurchaseDateInSeconds(),
      purchasePrice: pass.purchasePrice,
      serviceFees: pass.serviceFees,
      revenue: pass.revenue,
      paymentId: pass.paymentId.fold(() => null, item => item),
      state: pass.state,
      idx: pass.idx,
      language: pass.language,
      browser: pass.device.browser,
      device: pass.device.device,
      os: pass.device.os,
      archived: pass.archived,
      saleType: pass.saleType,
      urlDownload: urls.getDownload(),
      urlExport: urls.getExport(),
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
