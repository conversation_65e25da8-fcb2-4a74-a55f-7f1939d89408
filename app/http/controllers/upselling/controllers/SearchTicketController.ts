import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchTicketDto } from '@/upselling/tickets/application/contracts/SearchTicketDto';
import { FindTicketUseCase } from '@/upselling/tickets/application/use-cases/FindTicketUseCase';
import { searchTicketSchema } from '@app/http/@types/cli-api/upselling/search/schema';

import { parseSearchTicketRequest } from '../requests/SearchTicketRequest';
import { SearchTicketResponse } from '../responses/SearchTicketResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';

type Response = ReturnType<ReturnType<typeof SearchTicketResponse>['execute']>;

type Schema = typeof searchTicketSchema;

@singleton()
export class SearchTicketController {
  constructor(private readonly useCase: FindTicketUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto: SearchTicketDto = parseSearchTicketRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return await reply.code(code).send({ message });
    }

    const response = SearchTicketResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
