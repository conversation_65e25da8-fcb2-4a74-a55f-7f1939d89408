import { HTTP_CODES } from '@app/http/HttpCodes';

import type { TicketTypeSupplementPrimitives } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { successResponseSchema } from '@app/http/@types/cli-api/upselling/getSupplements/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const GetSupplementsResponse = (): IResponse<TicketTypeSupplementPrimitives[], Response> => {
  const execute = (dto: TicketTypeSupplementPrimitives[]): Response => {
    const supplements = {
      supplements: dto.map((supplement: TicketTypeSupplementPrimitives) => {
        const purchaseLimit = supplement.purchaseLimit.fold(
          () => null,
          purchaseLimit => ({
            minQuantity: purchaseLimit.minQuantity,
            maxQuantity: purchaseLimit.maxQuantity.fold(
              () => null,
              maxQuantity => maxQuantity,
            ),
            isUnlimited: purchaseLimit.isUnlimited,
          }),
        );

        const redemptionDeadline = supplement.redemptionDeadline.fold(
          () => null,
          redemptionDeadline => redemptionDeadline,
        );

        return {
          id: supplement.id,
          label: supplement.label,
          price: supplement.price,
          productQuantity: supplement.productQuantity,
          description: supplement.description,
          fakePrice: supplement.fakePrice ?? null,
          purchaseLimit,
          redemptionDeadline,
        };
      }),
    };

    return supplements;
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
