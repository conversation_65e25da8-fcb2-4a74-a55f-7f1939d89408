import { HTTP_CODES } from '@app/http/HttpCodes';

import type { LocationEntity } from '@/locations/domain/entities/LocationEntity';
import type { FindMicrositeResponseType, OrganizationData } from '@/microsite/domain/contracts/FindMicrositeResponse';
import type { Microsite } from '@/microsite/domain/entities/Microsite';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { Seo } from '@/seo/domain/entities/Seo';
import type { micrositeSuccessResponseSchema } from '@app/http/@types/cli-api/microsite/find/successResponseSchema';
import type { Static } from '@sinclair/typebox';
import type { IFindMicrositeResource, OrganizationDataSchema } from './FindMicrositeContracts';

export type FindMicrositeResponseDto = Static<typeof micrositeSuccessResponseSchema>;
export type UrlSchema = OrganizationDataSchema['urls'];
export type CoversSchema = OrganizationDataSchema['covers'];
export type LocationSchema = OrganizationDataSchema['defaultLocation'];
export type TrackingSchema = OrganizationDataSchema['tracking'];
export type SeoSchema = OrganizationDataSchema['seo'];
export type IframeSchema = OrganizationDataSchema['iframe'];
export type ImageSchema = OrganizationDataSchema['images'];

export const FindMicrositeOrganizationResponse = (): IFindMicrositeResource => {
  const execute = (findMicrosite: FindMicrositeResponseType): FindMicrositeResponseDto => {
    const { channel } = findMicrosite;

    const {
      organization, organizationConfiguration, location, seo, microsite, hasPassTypes, photos,
    } = findMicrosite.data as OrganizationData;

    const photosUrl: string[] = [];

    for (const photo of photos.values()) {
      if (photo.urlMedium) {
        photosUrl.push(photo.urlMedium);
      }
    }

    const covers = organization.areCoversDefined() ? makeCoversData(organization) : null;
    const urls = organization.areUrlsDefined() ? makeUrlData(organization) : null;

    let parentalAuthorization: string | null = null;
    let termsAndConditions: string | null = null;
    let bookingExtraPeoplePricing: string | null = null;
    let ticketCancellationTime: number | null = null;

    if (organizationConfiguration.isDefined()) {
      const _organizationConfiguration = organizationConfiguration.get();

      parentalAuthorization = _organizationConfiguration.parentalAuthorization.fold(() => null, item => item);
      termsAndConditions = _organizationConfiguration.termsConditions.fold(() => null, item => item);
      bookingExtraPeoplePricing = _organizationConfiguration.bookingExtraPeoplePricing;
      ticketCancellationTime = _organizationConfiguration.ticketCancellationTime;
    }

    return {
      channel,
      data: {
        id: organization.id,
        name: organization.name,
        type: organization.type,
        slug: organization.slug.fold(() => null, item => item),
        currency: organization.currency,
        countryCode: organization.countryCode,
        hosts: organization.hosts,
        images: organization.image.fold(() => null, item => makeImageData(item)),
        covers,
        usesDays: organization.usesDays,
        urls,
        tracking: microsite.isDefined() ? makeTrackingData(microsite.get()) : null,
        iframe: microsite.isDefined() ? makeIframeData(microsite.get()) : null,
        parentalAuthorization,
        termsAndConditions,
        bookingExtraPeoplePricing,
        ticketCancellationTime,
        hasPassTypes,
        photos: photosUrl,
        defaultLocation: location.fold(() => null, location => makeLocationData(location)),
        seo: seo.isDefined() ? makeSeoData(seo.get()) : null,
      },
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};

const makeUrlData = (organization: Organization): UrlSchema => {
  return {
    website: organization.web.fold(() => null, item => item),
    facebook: organization.facebook.fold(() => null, item => item),
    instagram: organization.instagram.fold(() => null, item => item),
    menuUrl: organization.menuUrl.fold(() => null, item => item),
  };
};

const makeImageData = (image: string): ImageSchema => {
  return {
    image,
    imageLogo: image,
  };
};

const makeCoversData = (organization: Organization): CoversSchema => {
  return {
    cover: organization.cover.fold(() => null, item => item),
    coverImages: organization.coverImages.fold(() => null, (item) => {
      return {
        mini: item.mini.fold(() => null, item => item),
        small: item.small.fold(() => null, item => item),
        medium: item.medium.fold(() => null, item => item),
      };
    }),
  };
};

const makeLocationData = (location: LocationEntity): LocationSchema => {
  return {
    id: location.id,
    fullAddress: location.addressComplete.fold(() => null, item => item),
    latitude: location.coordinates.latitude,
    longitude: location.coordinates.longitude,
    alias: location.alias.fold(() => null, item => item),
  };
};

const makeSeoData = (seo: Seo): SeoSchema => {
  return {
    title: seo.title.fold(() => null, item => item),
    keywords: seo.keywords.fold(() => null, item => item),
    place: seo.place.fold(() => null, item => item),
    descriptions: {
      short: seo.shortDescription.fold(() => null, item => item),
      lead: {
        relatedToPlace: seo.leadRelatedToPlaceDescription.fold(() => null, item => item),
        local: seo.leadLocalDescription.fold(() => null, item => item),
      },
      body: {
        relatedToPlace: seo.bodyRelatedToPlaceDescription.fold(() => null, item => item),
        local: seo.bodyLocalDescription.fold(() => null, item => item),
      },
    },
  };
};

const makeTrackingData = (microsite: Microsite): TrackingSchema => {
  return {
    facebook: {
      pixelId: microsite.facebookPixelId.fold(() => null, item => item),
      accessToken: microsite.facebookAccessToken.fold(() => null, item => item),
    },
    google: { propertyId: microsite.propertyIdGoogle.fold(() => null, item => item) },
  };
};

const makeIframeData = (microsite: Microsite): IframeSchema => {
  return {
    pages: {
      cancel: microsite.cancelPage.fold(() => null, item => item),
      theme: microsite.pageTheme.fold(() => null, item => item),
      thankYou: microsite.thankYouPage.fold(() => null, item => item),
    },
  };
};
