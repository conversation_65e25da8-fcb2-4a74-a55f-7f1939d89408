import { Maybe } from '@discocil/fv-domain-library/domain';

import { EPurchaseTicketsFlow } from '@/tickets/tickets/domain/enums/TicketEnum';

import type { PurchaseSupplement, PurchaseSupplements } from '@/tickets/tickets/domain/contracts/PurchaseContracts';
import type { PurchaseTicketsDto } from '@/tickets/tickets/domain/contracts/PurchaseTicketsDto';
import type { OneTypePurchaseTickets } from '@app/http/@types/cli-api/tickets/purchase/bodySchema';
import type { PurchaseTicketsRequest } from '@app/http/@types/cli-api/tickets/purchase/schema';

export const purchaseTicketsParser = (request: PurchaseTicketsRequest): PurchaseTicketsDto => {
  const body = request.body as OneTypePurchaseTickets;

  return {
    flow: EPurchaseTicketsFlow.ONE_TYPE,
    eventId: body.eventId,
    amount: body.amount,
    typeId: body.typeId,
    assistants: body.assistants.map((assistant) => {
      const supplements: PurchaseSupplements = new Map<string, PurchaseSupplement>();

      for (const supplement of assistant.supplements ?? []) {
        supplements.set(supplement.id, supplement);
      }

      return {
        supplements,
        fullname: assistant.fullname ?? null,
        email: assistant.email,
        emailConfirmation: assistant.emailConfirmation,
        phone: assistant.phone ?? null,
        birthDate: assistant.birthDate ?? null,
        gender: assistant.gender ?? null,
        personalDocument: assistant.personalDocument ?? null,
        address: assistant.address ?? null,
        country: assistant.country ?? null,
        zipCode: assistant.zipCode ?? null,
        answers: assistant.answers ?? [],
      };
    }),
    linkId: body.linkId,
    subscriberId: body.subscriberId,
    organizationAssignedId: body.organizationAssignedId,
    referrerId: body.referrerId,
    idx: body.idx,
    purchaseId: body.purchaseId,
    discountCode: body.discountCode,
    urls: body.urls,
    warrantySelected: body.warrantySelected,
    remarketing: body.remarketing,
    fb: body.fb,
    sitting: body.sitting,
    spotify: body.spotify,
    saleType: body.saleType,
    paylinkId: Maybe.fromValue(body.paylinkId),
    userAgent: request.fingerPrint.fingerPrint.browser.source,
    ip: request.fingerPrint.fingerPrint.ip,
    os: request.fingerPrint.fingerPrint.os.name,
    language: request.fingerPrint.fingerPrint.language.veryShort,
    browser: request.fingerPrint.fingerPrint.browser.name,
    device: request.fingerPrint.fingerPrint.device.name,
    applicationId: request.application.id,
  };
};
