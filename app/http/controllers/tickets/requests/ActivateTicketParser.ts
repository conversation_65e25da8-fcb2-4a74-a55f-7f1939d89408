import { Maybe } from '@discocil/fv-domain-library';

import type { ActivateTicketDto } from '@/tickets/tickets/domain/contracts/ActivateTicketContracts';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { activateTicketSchema } from '@app/http/@types/cli-api/tickets/activate/schema';

type Request = FastifyRequestTypebox<typeof activateTicketSchema>;

export const activateTicketParser = (request: Request): ActivateTicketDto => {
  const ticketId = request.params.id;

  const body = request.body;

  return {
    ticketId,
    assistant: {
      fullname: body.assistant.fullname ?? null,
      email: body.assistant.email,
      emailConfirmation: body.assistant.emailConfirmation,
      phone: body.assistant.phone ?? null,
      birthDate: body.assistant.birthDate ?? null,
      gender: body.assistant.gender ?? null,
      personalDocument: body.assistant.personalDocument ?? null,
      address: body.assistant.address ?? null,
      country: body.assistant.country ?? null,
      zipCode: body.assistant.zipCode ?? null,
      answers: body.assistant.answers ?? [],
      supplements: new Map(),
    },
    remarketing: body.remarketing,
    purchaseId: Maybe.fromValue(body.purchaseId),
    idx: Maybe.fromValue(body.idx),
    ip: Maybe.fromValue(request.fingerPrint.fingerPrint.ip),
    os: Maybe.fromValue(request.fingerPrint.fingerPrint.os.name),
    language: Maybe.fromValue(request.fingerPrint.fingerPrint.language.veryShort),
    browser: Maybe.fromValue(request.fingerPrint.fingerPrint.browser.name),
    device: Maybe.fromValue(request.fingerPrint.fingerPrint.device.name),
  };
};
