import { Maybe } from '@discocil/fv-domain-library/domain';

import { EPurchaseTicketsFlow } from '@/tickets/tickets/domain/enums/TicketEnum';

import type { PurchaseSupplement, PurchaseSupplements } from '@/tickets/tickets/domain/contracts/PurchaseContracts';
import type { PurchaseTicketsOrderDto } from '@/tickets/tickets/domain/contracts/PurchaseTicketsDto';
import type { OrderPurchaseTickets } from '@app/http/@types/cli-api/tickets/purchase/bodySchema';
import type { PurchaseTicketsRequest } from '@app/http/@types/cli-api/tickets/purchase/schema';

export const purchaseTicketsOrderParser = (request: PurchaseTicketsRequest): PurchaseTicketsOrderDto => {
  const body = request.body as OrderPurchaseTickets;


  return {
    flow: EPurchaseTicketsFlow.ORDER,
    types: body.types.map((item) => {
      return {
        typeId: item.typeId,
        assistants: item.assistants.map((assistant) => {
          const supplements: PurchaseSupplements = new Map<string, PurchaseSupplement>();

          for (const supplement of assistant.supplements ?? []) {
            supplements.set(supplement.id, supplement);
          }

          return {
            optionId: assistant.optionId,
            supplements,
            fullname: assistant.fullname ?? null,
            email: assistant.email,
            emailConfirmation: assistant.emailConfirmation,
            phone: assistant.phone ?? null,
            birthDate: assistant.birthDate ?? null,
            gender: assistant.gender ?? null,
            personalDocument: assistant.personalDocument ?? null,
            address: assistant.address ?? null,
            country: assistant.country ?? null,
            zipCode: assistant.zipCode ?? null,
            answers: assistant.answers ?? [],
          };
        }),
      };
    }),
    eventId: body.eventId,
    amount: body.amount,
    linkId: body.linkId,
    subscriberId: body.subscriberId,
    organizationAssignedId: body.organizationAssignedId,
    referrerId: body.referrerId,
    idx: body.idx,
    purchaseId: body.purchaseId,
    discountCode: body.discountCode,
    urls: body.urls,
    warrantySelected: body.warrantySelected,
    remarketing: body.remarketing,
    fb: body.fb,
    sitting: body.sitting,
    spotify: body.spotify,
    saleType: body.saleType,
    paylinkId: Maybe.fromValue(body.paylinkId),
    userAgent: request.fingerPrint.fingerPrint.browser.source,
    ip: request.fingerPrint.fingerPrint.ip,
    os: request.fingerPrint.fingerPrint.os.name,
    language: request.fingerPrint.fingerPrint.language.veryShort,
    browser: request.fingerPrint.fingerPrint.browser.name,
    device: request.fingerPrint.fingerPrint.device.name,
    applicationId: request.application.id,
  };
};
