import { singleton } from 'tsyringe';

import { MaintenanceError } from '@/cross-cutting/infrastructure/errors/MaintenanceError';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { ActivateTicketUseCase } from '@/tickets/tickets/application/use-cases/ActivateTicketUseCase';
import { ActivateTicketDto } from '@/tickets/tickets/domain/contracts/ActivateTicketContracts';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';

import { activateTicketParser } from '../requests/ActivateTicketParser';

import type {
  ActivateTicketReply,
  ActivateTicketRequest,
} from '@app/http/@types/cli-api/tickets/activate/schema';

@singleton()
export class ActivateTicketController {
  constructor(private readonly useCase: ActivateTicketUseCase) {}

  async handler(request: ActivateTicketRequest, reply: ActivateTicketReply): Promise<void> {
    if (config.maintenance.enabled) {
      const maintenanceError = MaintenanceError.build({ context: this.constructor.name });

      return await reply.code(HTTP_CODES.SERVICE_UNAVAILABLE_503).send({ message: maintenanceError.message });
    }

    const dto: ActivateTicketDto = activateTicketParser(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return await reply.code(code).send({ message });
    }

    return await reply.code(HTTP_CODES.NO_CONTENT_204).send();
  }
}
