import { FvDate } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import type { SearchPassTypesResponseType } from '@/passes/passTypes/domain/entities/PassType';
import type { Price } from '@/passes/prices/domain/entities/Price';
import type { passTypeSuccessSchema, successResponseSchema } from '@app/http/@types/cli-api/passType/search/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;
type PassTypeResponse = Static<typeof passTypeSuccessSchema>;

export const SearchPassTypesResponse = (): IResponse<SearchPassTypesResponseType, Response> => {
  const execute = (passTypesResponse: SearchPassTypesResponseType): Response['data'] => {
    const { passTypes, prices } = passTypesResponse;

    const filterActualPrice = (pricesIds: IdPrimitive[]): Price | null => {
      const [firstPriceId] = pricesIds;

      if (!firstPriceId) {
        return null;
      }

      const firstPrice = prices.get(firstPriceId);

      return firstPrice ?? null;
    };

    const passTypesResult: Response['data'] = [];

    for (const passType of passTypes.values()) {
      const price = filterActualPrice(passType.prices);

      if (price === null) {
        continue;
      }

      const passTypeResult: PassTypeResponse = {
        id: passType.id,
        organizationId: passType.organizationId,
        name: passType.name,
        startDate: FvDate.create(passType.startDate).toSeconds(),
        endDate: FvDate.create(passType.endDate).toSeconds(),
        startCountdown: passType.startCountdown,
        color: passType.color,
        tags: passType.tags,
        designId: passType.designId.fold(() => null, item => item),
        totalLimit: passType.totals.limit,
        totalSold: passType.totals.sold,
        defaultConditionId: passType.defaultConditionId.fold(() => null, item => item),
        published: passType.published,
        archived: passType.archived,
        manualStopMessage: passType.message.manualStop.fold(() => null, item => item),
        autoLimitMessage: passType.message.autoLimit,
        autoEndMessage: passType.message.autoEnd,
        description: passType.description.fold(() => null, item => item),
        saleTypes: passType.saleTypes,
        price: {
          id: price.id,
          description: price.description.fold(() => null, item => item),
          price: price.price,
          fee: price.serviceFees.quantity,
          feeType: price.serviceFees.type,
          maximumNumberPass: price.maximumNumberPass,
          additionalInfo: price.additionalInfo.fold(() => null, item => item),
        },
      };

      passTypesResult.push(passTypeResult);
    }

    return passTypesResult;
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
