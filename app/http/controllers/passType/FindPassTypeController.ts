import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindPassTypeUseCase } from '@/passes/passTypes/application/FindPassTypeUseCase';

import { FindPassTypeResponse } from './responses/FindPassTypeResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { getPassTypeSchema } from '@app/http/@types/cli-api/passType/find/schema';

type Response = ReturnType<ReturnType<typeof FindPassTypeResponse>['execute']>;

type Schema = typeof getPassTypeSchema;

@singleton()
export class FindPassTypeController {
  constructor(private readonly service: FindPassTypeUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const { id } = request.params;
    const { organizationId } = request.query;

    const useCaseResult = await this.service.execute({
      id,
      organizationId,
    });

    if (useCaseResult.isLeft()) {
      const { code, message } = getErrorInfo(useCaseResult.value);

      return reply.code(code).send({ message });
    }

    const response = FindPassTypeResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
