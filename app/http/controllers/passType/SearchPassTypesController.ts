import { EPassTypeSaleTypes } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchPassTypesUseCase } from '@/passes/passTypes/application/SearchPassTypesUseCase';
import { Price } from '@/passes/prices/domain/entities/Price';

import { parseSearchPassTypesRequest } from './requests/SearchPassTypeRequest';
import { SearchPassTypesResponse } from './responses/SearchPassTypesResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchPassTypeSchema } from '@app/http/@types/cli-api/passType/search/schema';

export type GetPassTypeDTO = {
  id: string;
  organizationId: string;
  name: string;
  price: Price | undefined;
  color?: string;
  startDate?: number;
  endDate?: number;
  startCountdown?: boolean;
  tags?: string[];
  designId?: string;
  totalLimit?: number;
  totalSold?: number;
  defaultConditionId?: string;
  published?: boolean;
  archived?: boolean;
  fields?: string[];
  manualStopMessage?: string;
  autoLimitMessage?: string;
  autoEndMessage?: string;
  description?: string;
  saleTypes?: EPassTypeSaleTypes[];
};

export type GetPassTypesDTO = GetPassTypeDTO[];

type Schema = typeof searchPassTypeSchema;

type Response = ReturnType<ReturnType<typeof SearchPassTypesResponse>['execute']>;

@singleton()
export class SearchPassTypesController {
  constructor(private readonly useCase: SearchPassTypesUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = parseSearchPassTypesRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return reply.code(code).send({ message });
    }

    const response = SearchPassTypesResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
