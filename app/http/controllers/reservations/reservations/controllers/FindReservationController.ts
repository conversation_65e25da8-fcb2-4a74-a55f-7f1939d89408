import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindReservationUseCase } from '@/reservations/reservations/application/use-cases/FindReservationUseCase';
import { FindReservationResponse } from '@app/http/controllers/reservations/reservations/responses/FindReservationResponse';

import type {
  FindReservationUseCaseReply as Reply,
  FindReservationUseCaseRequest as Request,
} from '../contracts/FindReservationContract';

type Response = ReturnType<ReturnType<typeof FindReservationResponse>['execute']>;

@singleton()
export class FindReservationController {
  constructor(private readonly useCase: FindReservationUseCase) {}

  @cacheController()
  async handler(request: Request, reply: Reply): Promise<Response> {
    const dto = { activationCode: request.params.activationCode };

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return await reply.code(code).send({ message });
    }

    const response = FindReservationResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
