import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchFormFieldsUseCase } from '@/reservations/reservations/application/use-cases/SearchFormFieldsUseCase';
import { SearchFormFieldsRequest } from '@app/http/controllers/reservations/reservations/requests/SearchFormFieldsRequest';
import { SearchFormFieldsResponse } from '@app/http/controllers/reservations/reservations/responses/SearchFormFieldsResponse';

import type {
  SearchFormFieldsUseCaseReply as Reply,
  SearchFormFieldsUseCaseRequest as Request,
} from '../contracts/SearchFormFieldsContract';

type Response = ReturnType<ReturnType<typeof SearchFormFieldsResponse>['execute']>;

@singleton()
export class SearchFormFieldsController {
  constructor(private readonly useCase: SearchFormFieldsUseCase) {}

  @cacheController()
  async handler(request: Request, reply: Reply): Promise<Response> {
    const requestParsed = SearchFormFieldsRequest.parser(request);

    const useCaseResult = await this.useCase.execute(requestParsed);

    if (useCaseResult.isLeft()) {
      const { code, message } = getErrorInfo(useCaseResult.value);

      return await reply.code(code).send({ message });
    }

    const response = SearchFormFieldsResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
