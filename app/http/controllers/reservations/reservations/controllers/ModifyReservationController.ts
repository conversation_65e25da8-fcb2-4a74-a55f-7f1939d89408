import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { ModifyReservationUseCase } from '@/reservations/reservations/application/use-cases/ModifyReservationUseCase';
import { ModifyReservationRequest } from '@app/http/controllers/reservations/reservations/requests/ModifyReservationRequest';
import { ModifyReservationResponse } from '@app/http/controllers/reservations/reservations/responses/ModifyReservationResponse';

import type {
  ModifyReservationUseCaseReply as Reply,
  ModifyReservationUseCaseRequest as Request,
} from '../contracts/ModifyReservationContract';

type Response = ReturnType<ReturnType<typeof ModifyReservationResponse>['execute']>;

@singleton()
export class ModifyReservationController {
  constructor(private readonly useCase: ModifyReservationUseCase) {}

  async handler(request: Request, reply: Reply): Promise<Response> {
    const dto = ModifyReservationRequest.parser(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return await reply.code(code).send({ message });
    }

    const response = ModifyReservationResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
