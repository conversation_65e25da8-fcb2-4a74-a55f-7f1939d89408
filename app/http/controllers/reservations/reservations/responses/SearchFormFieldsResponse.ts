import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Collection } from '@discocil/fv-domain-library/domain';
import type { FormField } from '@/reservations/reservations/domain/entities/FormField';
import type { successResponseSchema } from '@app/http/@types/cli-api/reservations/form-fields/search/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const SearchFormFieldsResponse = (): IResponse<Collection<FormField>, Response> => {
  const execute = (formFields: Collection<FormField>): Response => {
    return formFields.toArray().map((field) => {
      const type = field.placeholder.isDefined()
        ? { type: field.type, placeholder: field.placeholder.fold(() => null, value => value) }
        : { type: field.type, options: field.options };

      return {
        id: field.id,
        value: field.response,
        configuration: {
          canBeUpdated: field.canBeUpdated,
          isRequired: field.isRequired,
          title: field.title,
          type,
        },
      };
    });
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
