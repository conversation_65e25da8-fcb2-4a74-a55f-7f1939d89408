import { HTTP_CODES } from '@app/http/HttpCodes';

import type { FormFieldPrimitives } from '@/reservations/reservations/domain/entities/FormField';
import type { Reservation } from '@/reservations/reservations/domain/entities/Reservation';
import type { successResponseSchema } from '@app/http/@types/cli-api/reservations/reservations/find/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

type FormFieldsResponse = {
  id: string;
  value: unknown;
  configuration: {
    canBeUpdated: boolean;
    isRequired: boolean;
    title: string;
    type: { type: string; placeholder: string | null; } | { type: string; options: string[]; };
  };
};

export const FindReservationResponse = (): IResponse<Reservation, Response> => {
  const execute = (reservation: Reservation): Response => {
    const pendingDepositPayment = reservation.getPendingDepositPayment();
    const pendingFullPayment = reservation.allowCompletePayment
      ? reservation.getPendingFullPayment()
      : reservation.getPendingDepositPayment();

    return {
      reservationId: reservation.id,
      organizationId: reservation.organizationId,
      eventId: reservation.eventId,
      state: reservation.state,
      numberOfPeople: reservation.numberOfPeople,
      allowCompletePayment: reservation.allowCompletePayment,
      price: reservation.price.amount,
      currency: reservation.price.currency,
      totalAmount: reservation.getTotalAmount().toDecimal(),
      processedPayments: reservation.getProcessedPaymentsPrimitives().map(payment => ({
        date: payment.date,
        method: payment.method,
        amount: payment.totalAmount.amount,
        channel: payment.channel,
      })),
      pendingFullPayment: {
        baseAmount: pendingFullPayment.baseAmount.toDecimal(),
        feeAmount: pendingFullPayment.feeAmount.toDecimal(),
        totalAmount: pendingFullPayment.totalAmount.toDecimal(),
      },
      pendingDepositPayment: {
        baseAmount: pendingDepositPayment.baseAmount.toDecimal(),
        feeAmount: pendingDepositPayment.feeAmount.toDecimal(),
        totalAmount: pendingDepositPayment.totalAmount.toDecimal(),
      },
      customerFields: buildCustomerFieldsResponse(reservation.getActiveFormFields()),
    };
  };

  const buildCustomerFieldsResponse = (fields: FormFieldPrimitives[]): FormFieldsResponse[] => {
    return fields.map((field) => {
      const type = field.placeholder.isDefined()
        ? { type: field.type, placeholder: field.placeholder.fold(() => null, value => value) }
        : { type: field.type, options: field.options };

      return {
        id: field.id,
        value: field.response,
        configuration: {
          canBeUpdated: field.canBeUpdated,
          isRequired: field.isRequired,
          title: field.title,
          type,
        },
      };
    });
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
