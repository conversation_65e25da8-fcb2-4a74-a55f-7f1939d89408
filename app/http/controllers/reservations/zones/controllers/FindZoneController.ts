import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindZoneUseCase } from '@/reservations/zones/application/use-cases/FindZoneUseCase';
import { FindZoneRequest } from '@app/http/controllers/reservations/zones/requests/FindZoneRequest';
import { FindZoneResponse } from '@app/http/controllers/reservations/zones/responses/FindZoneResponse';

import type {
  FindZoneUseCaseReply as Reply,
  FindZoneUseCaseRequest as Request,
} from '../contracts/FindZoneContract';

type Response = ReturnType<ReturnType<typeof FindZoneResponse>['execute']>;

@singleton()
export class FindZoneController {
  constructor(private readonly useCase: FindZoneUseCase) {}

  async handler(request: Request, reply: Reply): Promise<Response> {
    const dto = FindZoneRequest.parser(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const { code, message } = getErrorInfo(useCaseResult.value);

      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      return await reply.code(code).send({ message });
    }

    const response = FindZoneResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
