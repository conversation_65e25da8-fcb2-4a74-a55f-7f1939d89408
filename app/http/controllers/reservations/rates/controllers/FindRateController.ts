import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindRateUseCase } from '@/reservations/rates/application/use-cases/FindRateUseCase';
import { FindRateRequest } from '@app/http/controllers/reservations/rates/requests/FindRateRequest';
import { FindRateResponse } from '@app/http/controllers/reservations/rates/responses/FindRateResponse';

import type {
  FindRateUseCaseReply as Reply,
  FindRateUseCaseRequest as Request,
} from '../contracts/FindRateContract';

type Response = ReturnType<ReturnType<typeof FindRateResponse>['execute']>;

@singleton()
export class FindRateController {
  constructor(private readonly useCase: FindRateUseCase) {}

  async handler(request: Request, reply: Reply): Promise<Response> {
    const requestParsed = FindRateRequest.parser(request);

    const useCaseResult = await this.useCase.execute(requestParsed);

    if (useCaseResult.isLeft()) {
      const { code, message } = getErrorInfo(useCaseResult.value);

      return await reply.code(code).send({ message });
    }

    const response = FindRateResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
