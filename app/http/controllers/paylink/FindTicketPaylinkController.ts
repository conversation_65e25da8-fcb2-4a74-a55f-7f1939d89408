import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindTicketPaylinkUseCase } from '@/paylinks/application/FindTicketPaylinkUseCase';
import { FindPaylinkDTO } from '@/paylinks/domain/contracts/FindPaylinkDtoContract';

import { FindTicketPaylinkResponse } from './responses/tickets/FindTicketPaylinkResponse';

import type {
  FindPaylinkReply,
  FindPaylinkRequest,
  FindPaylinkReplyResponse as Response,
} from '@app/http/@types/cli-api/paylinks/tickets/find/schema';

@singleton()
export class FindTicketPaylinkController {
  constructor(private readonly useCase: FindTicketPaylinkUseCase) { }

  async handler(request: FindPaylinkRequest, reply: FindPaylinkReply): Promise<Response> {
    const dto: FindPaylinkDTO = { activateCode: request.params.activateCode };

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return reply.code(code).send({ message });
    }

    const response = FindTicketPaylinkResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
