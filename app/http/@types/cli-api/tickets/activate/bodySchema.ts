import { Type } from '@sinclair/typebox';

import { assistant } from '../purchase/bodySchema';

import type { Static } from '@sinclair/typebox';

export const activateTicketSchema = Type.Object({
  assistant,
  remarketing: Type.Boolean(),
  purchaseId: Type.Optional(Type.String()),
  idx: Type.Optional(Type.String({ maxLength: 32 })),
});

export const bodySchema = activateTicketSchema;

export type ActivateTicketBody = Static<typeof bodySchema>;
