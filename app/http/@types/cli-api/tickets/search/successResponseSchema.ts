import { Type } from '@sinclair/typebox';

import { PaginationResponse } from '../../cross-cutting/PaginationSchemas';
import { Nullable } from '../../cross-cutting/schema';
import { customerSchema, urlsSchema } from '../find/successResponseSchema';

const typeSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  hideCountdown: Type.Boolean(),
});

const eventSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  date: Type.Number(),
});

const paymentSchema = Type.Object({
  id: Type.String(),
  totalImport: Type.Number(),
  feesTotal: Type.Number(),
  currency: Type.String(),
});

const optionSchema = Type.Object({ price: Type.Number() });

export const successSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    organizationId: Type.String(),
    purchaseId: Nullable(Type.String()),
    type: typeSchema,
    event: eventSchema,
    payment: Nullable(paymentSchema),
    code: Type.String(),
    state: Type.String(),
    nominative: Type.Boolean(),
    option: optionSchema,
    urls: urlsSchema,
    customer: customerSchema,
    remarketing: Type.Boolean(),
  }),
);

export const successResponseSchema = PaginationResponse(successSchema);
