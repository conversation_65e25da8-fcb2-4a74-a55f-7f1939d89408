import { Type } from '@sinclair/typebox';

export const successResponseSchema = Type.Array(
  Type.Object({
    value: Type.Unknown(),
    configuration: Type.Object({
      canBeUpdated: Type.Boolean(),
      isRequired: Type.Boolean(),
      title: Type.String(),
      type: Type.Union([
        Type.Object({
          type: Type.String(),
          placeholder: Type.Union([
            Type.String(),
            Type.Null(),
          ]),
        }),
        Type.Object({
          type: Type.String(),
          options: Type.Array(Type.String()),
        }),
      ]),
    }),
  }),
);
