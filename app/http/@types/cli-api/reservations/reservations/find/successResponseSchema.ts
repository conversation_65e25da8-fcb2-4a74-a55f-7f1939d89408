import { Type } from '@sinclair/typebox';

export const successResponseSchema = Type.Object({
  reservationId: Type.String(),
  organizationId: Type.String(),
  eventId: Type.String(),
  state: Type.String(),
  numberOfPeople: Type.Number(),
  allowCompletePayment: Type.Boolean(),
  price: Type.Number(),
  currency: Type.String(),
  totalAmount: Type.Number(),
  processedPayments: Type.Array(
    Type.Object({
      date: Type.String(),
      method: Type.String(),
      amount: Type.Number(),
    }),
  ),
  pendingFullPayment: Type.Object({
    baseAmount: Type.Number(),
    feeAmount: Type.Number(),
    totalAmount: Type.Number(),
  }),
  pendingDepositPayment: Type.Object({
    baseAmount: Type.Number(),
    feeAmount: Type.Number(),
    totalAmount: Type.Number(),
  }),
  customerFields: Type.Array(
    Type.Object({
      value: Type.Unknown(),
      configuration: Type.Object({
        canBeUpdated: Type.Boolean(),
        isRequired: Type.Boolean(),
        title: Type.String(),
        type: Type.Union([
          Type.Object({
            type: Type.String(),
            placeholder: Type.Union([
              Type.String(),
              Type.Null(),
            ]),
          }),
          Type.Object({
            type: Type.String(),
            options: Type.Array(Type.String()),
          }),
        ]),
      }),
    }),
  ),
});
