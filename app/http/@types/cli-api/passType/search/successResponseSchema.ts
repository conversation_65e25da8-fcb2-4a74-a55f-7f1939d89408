import { Type } from '@sinclair/typebox';

import { PaginationResponse } from '../../cross-cutting/PaginationSchemas';
import { Nullable } from '../../cross-cutting/schema';

export const passTypeSuccessSchema = Type.Object({
  id: Type.String(),
  organizationId: Type.String(),
  name: Type.String(),
  startDate: Type.Number(),
  endDate: Type.Number(),
  startCountdown: Type.Boolean(),
  description: Nullable(Type.String()),
  price: Type.Object({
    id: Type.String(),
    description: Nullable(Type.String()),
    price: Type.Number(),
    fee: Type.Number(),
    feeType: Type.String(),
    maximumNumberPass: Type.Number(),
    additionalInfo: Nullable(Type.String()),
  }),
  color: Type.String(),
  autoEndMessage: Type.String(),
  tags: Type.Array(Type.String()),
  saleTypes: Nullable(Type.Array(Type.String())),
  designId: Nullable(Type.String()),
  totalLimit: Type.Number(),
  totalSold: Type.Number(),
  defaultConditionId: Nullable(Type.String()),
  published: Type.Boolean(),
  archived: Type.Boolean(),
  manualStopMessage: Nullable(Type.String()),
  autoLimitMessage: Type.String(),
});

export const successSchema = Type.Array(passTypeSuccessSchema);

export const successResponseSchema = PaginationResponse(successSchema);
