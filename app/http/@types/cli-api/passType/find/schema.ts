import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

export const querySchema = Type.Object({ organizationId: Type.String() });

export const paramsSchema = Type.Object({ id: Type.String() });

export const successResponseSchema = Type.Object({
  id: Type.String(),
  organizationId: Type.String(),
  name: Type.String(),
  startDate: Type.Number(),
  endDate: Type.Number(),
  startCountdown: Type.Boolean(),
  color: Type.String(),
  tags: Type.Array(Type.String()),
  designId: Nullable(Type.String()),
  totalLimit: Type.Number(),
  totalSold: Type.Number(),
  defaultConditionId: Nullable(Type.String()),
  published: Type.Boolean(),
  archived: Type.Boolean(),
  manualStopMessage: Nullable(Type.String()),
  autoLimitMessage: Type.String(),
  autoEndMessage: Type.String(),
  fields: Nullable(Type.Record(Type.String(), Type.Unknown())),
  description: Nullable(Type.String()),
  prices: Type.Array(
    Type.Object({
      id: Type.String(),
      description: Nullable(Type.String()),
      price: Type.Number(),
      fee: Type.Number(),
      feeType: Type.String(),
      maximumNumberPass: Type.Number(),
      additionalInfo: Nullable(Type.String()),
    }),
  ),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const getPassTypeSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
