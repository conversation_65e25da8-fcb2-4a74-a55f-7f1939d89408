import {
  ECountryCode,
  EDocumentType,
  EGender,
} from '@discocil/fv-domain-library/domain';
import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

const customer = {
  name: Type.String(),
  email: Type.String(),
  phone: Type.Optional(Type.String()),
  address: Type.Optional(Type.String()),
  dateOfBirth: Type.Optional(Type.Number()),
  country: Type.Optional(Type.Enum(ECountryCode)),
  postalCode: Type.Optional(Type.String()),
  gender: Type.Optional(Type.Enum(EGender)),
  typeIdentity: Type.Optional(Type.Enum(EDocumentType)),
  identity: Type.Optional(Type.String()),
  image: Type.Optional(Type.String()),
  customer_custom_fields1: Type.Optional(Type.String()),
  customer_custom_fields2: Type.Optional(Type.String()),
};

const passes = {
  passTypeId: Type.String(),
  priceId: Type.String(),
  customer: Type.Object(customer),
};

export const purchasePassBodySchema = Type.Object({
  organizationId: Type.String(),
  redirect_url: Type.String(),
  error_url: Type.String(),
  idx: Type.String(),
  referrerId: Nullable(Type.String()),
  passes: Type.Array(Type.Object(passes)),
  sourceUrl: Nullable(Type.String()),
  remarketing: Type.Boolean(),
  paylink: Nullable(Type.String()),
});
