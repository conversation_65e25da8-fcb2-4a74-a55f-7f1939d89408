import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

export const purchasePassSuccessResponseSchema = Type.Object({
  inserted: Type.Array(
    Type.Object({
      id: Type.String(),
      organizationId: Type.String(),
      applicationId: Type.String(),
      organizationAssignedId: Nullable(Type.String()),
      customer: Nullable(
        Type.Object({
          fullname: Nullable(Type.String()),
          image: Nullable(Type.String()),
          email: Type.String(),
          phone: Nullable(Type.String()),
          gender: Nullable(Type.String()),
          personalDocumentNumber: Nullable(Type.String()),
          personalDocumentType: Nullable(Type.String()),
          country: Nullable(Type.String()),
          address: Nullable(Type.String()),
          birthday: Nullable(Type.Number()),
          postalCode: Nullable(Type.String()),
          customer_custom_fields1: Nullable(Type.String()),
          customer_custom_fields2: Nullable(Type.String()),
        }),
      ),
      typeId: Nullable(Type.String()),
      typeName: Nullable(Type.String()),
      typeColor: Nullable(Type.String()),
      priceId: Nullable(Type.String()),
      purchaseDate: Nullable(Type.Number()),
      purchasePrice: Nullable(Type.Number()),
      serviceFees: Nullable(Type.Number()),
      revenue: Nullable(Type.Number()),
      paymentId: Nullable(Type.String()),
      state: Type.String(),
      idx: Nullable(Type.String()),
      language: Type.String(),
      browser: Nullable(Type.String()),
      device: Nullable(Type.String()),
      os: Nullable(Type.String()),
      saleType: Type.String(),
    }),
  ),
  ids: Type.Array(Nullable(Type.String())),
  needPayment: Type.Boolean(),
  payment_url: Nullable(Type.String()),
});
