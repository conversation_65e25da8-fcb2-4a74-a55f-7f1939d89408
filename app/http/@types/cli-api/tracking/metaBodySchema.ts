import { Type } from '@sinclair/typebox';

import { EEventTypes } from '@/tracking/domain/value-objects/EventType';

import { Nullable } from '../cross-cutting/schema';

import type { Static } from '@sinclair/typebox';

const bodyPageViewSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  event_id: Type.String(),
  external_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  route: Type.String(),
  fbclid: Nullable(Type.String()),
  fbc: Nullable(Type.String()),
  fbp: Nullable(Type.String()),
  user_name: Nullable(Type.String()),
  user_email: Nullable(Type.String()),
  user_phone: Nullable(Type.String()),
});

const bodyViewContentSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  event_id: Type.String(),
  external_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  route: Type.String(),
  fbclid: Nullable(Type.String()),
  fbc: Nullable(Type.String()),
  fbp: Nullable(Type.String()),
  user_name: Nullable(Type.String()),
  user_email: Nullable(Type.String()),
  user_phone: Nullable(Type.String()),
  content_date: Type.Number(),
  content_name: Type.String(),
});

const itemSchema = Type.Object({
  id: Type.String(),
  price: Type.Number(),
  quantity: Type.Number(),
  category: Type.String(),
});

const bodyAddToCartSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  event_id: Type.String(),
  external_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  fbclid: Nullable(Type.String()),
  fbc: Nullable(Type.String()),
  fbp: Nullable(Type.String()),
  user_name: Nullable(Type.String()),
  user_email: Nullable(Type.String()),
  user_phone: Nullable(Type.String()),
  content_id: Type.String(),
  content_date: Type.Number(),
  content_name: Type.String(),
  content_type: Type.String(),
  content_ids: Type.Array(Type.String()),
  price: Type.Number(),
  value: Type.Number(),
  currency: Type.String(),
  num_items: Type.Number(),
  items: Type.Array(itemSchema),
});

const bodyInitiateCheckoutSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  event_id: Type.String(),
  external_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  fbclid: Nullable(Type.String()),
  fbc: Nullable(Type.String()),
  fbp: Nullable(Type.String()),
  user_name: Nullable(Type.String()),
  user_email: Nullable(Type.String()),
  user_phone: Nullable(Type.String()),
  content_id: Type.String(),
  content_name: Type.String(),
  content_type: Type.String(),
  content_ids: Type.Array(Type.String()),
  price: Type.Number(),
  value: Type.Number(),
  currency: Type.String(),
  num_items: Type.Number(),
  items: Type.Array(itemSchema),
});

const bodyPurchaseSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  event_id: Type.String(),
  external_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  fbclid: Nullable(Type.String()),
  fbc: Nullable(Type.String()),
  fbp: Nullable(Type.String()),
  user_name: Nullable(Type.String()),
  user_email: Nullable(Type.String()),
  user_phone: Nullable(Type.String()),
  content_id: Type.String(),
  content_name: Type.String(),
  content_type: Type.String(),
  content_ids: Type.Array(Type.String()),
  price: Type.Number(),
  value: Type.Number(),
  currency: Type.String(),
  num_items: Type.Number(),
  items: Type.Array(itemSchema),
});

export const metaBodySchema = Type.Union([
  bodyPageViewSchema,
  bodyViewContentSchema,
  bodyAddToCartSchema,
  bodyInitiateCheckoutSchema,
  bodyPurchaseSchema,
]);

export type PageViewRequest = Static<typeof bodyPageViewSchema>;
export type ViewContentRequest = Static<typeof bodyViewContentSchema>;
export type AddToCartRequest = Static<typeof bodyAddToCartSchema>;
export type InitiateCheckoutRequest = Static<typeof bodyInitiateCheckoutSchema>;
export type PurchaseRequest = Static<typeof bodyPurchaseSchema>;
