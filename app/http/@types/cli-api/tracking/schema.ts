import { Type } from '@sinclair/typebox';

import { EEventChannel } from '@/tracking/domain/value-objects/EventType';

import { bodySchema } from './bodySchema';
import { fvBodySchema } from './fvBodySchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../cross-cutting/schema';

const paramsSchema = Type.Object({ channel: Type.Enum(EEventChannel) });

const errorResponseSchema = Type.Object({ message: Type.String() });

const successResponseSchema = Type.Object({});

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const trackingTypeSchema = {
  params: paramsSchema,
  body: bodySchema,
  response: responseSchema,
};

export const fvTrackingTypeSchema = {
  params: paramsSchema,
  body: fvBodySchema,
  response: responseSchema,
};

type Schema = typeof trackingTypeSchema;
type fvSchema = typeof fvTrackingTypeSchema;

export type TrackingRequest = FastifyRequestTypebox<Schema>;
export type FvTrackingRequest = FastifyRequestTypebox<fvSchema>;
export type TrackingReply = FastifyReplyTypebox<Schema>;
