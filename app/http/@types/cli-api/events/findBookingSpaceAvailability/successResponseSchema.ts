import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

export const successResponseSchema = Type.Object({
  collaborator: Type.Object({
    name: Nullable(Type.String()),
    image: Nullable(Type.String()),
    phone: Nullable(Type.String()),
  }),
  organization: Type.Object({
    name: Type.String(),
    image: Nullable(Type.String()),
    slug: Nullable(Type.String()),
    floorImage: Nullable(Type.String()),
  }),
  name: Type.String(),
  date: Type.String(),
  zones: Type.Array(Type.Object({
    name: Type.String(),
    spaces: Type.Array(Type.Object({
      name: Type.String(),
      capacity: Type.Number(),
      isAvailable: Type.Boolean(),
      currency: Type.String(),
      price: Nullable(Type.Number()),
    })),
  })),
});
