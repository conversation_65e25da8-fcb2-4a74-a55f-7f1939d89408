import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

const organizerSchema = {
  name: Type.String(),
  cif: Nullable(Type.String()),
  address: Type.String(),
  postalCode: Type.String(),
  city: Nullable(Type.String()),
  province: Type.String(),
};

const datesSchema = {
  canceled: Nullable(Type.Number()),
  date: Type.Number(),
  end: Type.Number(),
  limitSale: Type.Number(),
  start: Type.Number(),
};

const artistsSchema = {
  name: Type.String(),
  image: Type.String(),
};

const imagesSchema = {
  medium: Nullable(Type.String()),
  small: Nullable(Type.String()),
};

const locationSchema = {
  province: Nullable(Type.String()),
  address: Nullable(Type.String()),
  addressComplete: Nullable(Type.String()),
  alias: Nullable(Type.String()),
  number: Nullable(Type.String()),
  postalCode: Nullable(Type.String()),
  municipality: Nullable(Type.String()),
  country: Nullable(Type.String()),
  coordinates: Type.Object({
    latitude: Type.Number(),
    longitude: Type.Number(),
  }),
  timezone: Nullable(Type.Object({
    id: Type.String(),
    name: Type.String(),
    dstOffset: Nullable(Type.Number()),
    rawOffset: Nullable(Type.Number()),
  })),
};

const organizationSchema = {
  id: Type.String(),
  name: Nullable(Type.String()),
  slug: Nullable(Type.String()),
  image: Nullable(Type.String()),
  cover: Nullable(Type.String()),
  floorImage: Nullable(Type.String()),
};

const preregisterSchema = { isActive: Type.Boolean() };

export const successResponseSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  code: Type.String(),
  organization: Type.Object(organizationSchema),
  organizer: Nullable(Type.Object(organizerSchema)),
  slug: Type.String(),
  location: Nullable(Type.Object(locationSchema)),
  description: Nullable(Type.String()),
  imagePlan: Nullable(Type.String()),
  age: Type.Number(),
  visible: Type.Boolean(),
  perch: Type.String(),
  qrMenuId: Nullable(Type.String()),
  image: Nullable(Type.String()),
  tzName: Nullable(Type.String()),
  organizerTerms: Nullable(Type.String()),
  isReceptionSingen: Type.Boolean(),
  isSittingEnabled: Type.Boolean(),
  isQualityRequired: Type.Boolean(),
  rrppCanCancel: Type.Boolean(),
  hasEmailReconfirmation: Type.Boolean(),
  areFeesShownUpfront: Type.Boolean(),
  currency: Type.String(),
  dates: Type.Object(datesSchema),
  musicalGenres: Type.Array(Type.String()),
  services: Type.Array(Type.String()),
  atmosphere: Type.Array(Type.String()),
  artists: Type.Array(Type.Object(artistsSchema)),
  images: Nullable(Type.Object(imagesSchema)),
  parentalAuthorization: Nullable(Type.String()),
  isPrivate: Type.Boolean(),
  preregister: Type.Object(preregisterSchema),
});
