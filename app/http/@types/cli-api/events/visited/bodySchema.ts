import { ECountryCode, EGender } from '@discocil/fv-domain-library/domain';
import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

const customer = {
  country: Type.Optional(Type.Enum(ECountryCode)),
  gender: Type.Optional(Type.Enum(EGender)),
  birthDate: Type.Optional(Type.Number()),
};

export const bodySchema = Type.Object({
  referrerId: Nullable(Type.String()),
  idx: Type.String(),
  customer: Type.Optional(Type.Object(customer)),
});
