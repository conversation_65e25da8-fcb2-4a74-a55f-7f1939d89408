import { stringify } from '@/cross-cutting/domain/helpers/stringify';

import { HTTP_CODES } from './HttpCodes';

import type { Logger } from '@/cross-cutting/infrastructure/loggers/Logger';
import type {
  FastifyBaseLogger,
  FastifyError,
  FastifyReply,
  FastifyRequest,
} from 'fastify';

type ErrorRequest = {
  serverError: FastifyError;
  request: FastifyRequest;
  reply: FastifyReply;
  fastifyLogger: FastifyBaseLogger;
  logger: Logger;
};

export const errorHandler = (dto: ErrorRequest): unknown => {
  const {
    serverError, request, reply, fastifyLogger, logger,
  } = dto;

  let { statusCode } = serverError;
  const response = { message: serverError.message };

  if (serverError.validation) {
    statusCode ||= HTTP_CODES.BAD_REQUEST_400;

    serverError.validation.forEach((error) => {
      fastifyLogger.warn(error);
    });

    return reply.status(statusCode).send(response);
  }

  statusCode ||= HTTP_CODES.INTERNAL_SERVER_ERROR_500;

  const isServerError = statusCode >= HTTP_CODES.INTERNAL_SERVER_ERROR_500;

  if (isServerError) {
    const { config } = request.routeOptions;
    const context = { config };

    const curtomError = request.error ?? `Untracked error: ${stringify(serverError.message)}`;

    logger.error(curtomError, context);
  }

  return reply.status(statusCode).send(response);
};
