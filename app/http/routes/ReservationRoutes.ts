import { singleton } from 'tsyringe';
import { type FastifyInstance } from 'fastify';

import { searchZonesSchema } from '@app/http/@types/cli-api/reservations/zones/search/schema';
import { SearchZonesController } from '@app/http/controllers/reservations/zones/controllers/SearchZonesController';
import { findZoneSchema } from '@app/http/@types/cli-api/reservations/zones/find/schema';
import { FindZoneController } from '@app/http/controllers/reservations/zones/controllers/FindZoneController';
import { findRateSchema } from '@app/http/@types/cli-api/reservations/rates/find/schema';
import { searchFormFieldsSchema } from '@app/http/@types/cli-api/reservations/form-fields/search/schema';
import { SearchFormFieldsController } from '@app/http/controllers/reservations/reservations/controllers/SearchFormFieldsController';

import { FindReservationController } from '../controllers/reservations/reservations/controllers/FindReservationController';
import { findReservationSchema } from '../@types/cli-api/reservations/reservations/find/schema';
import { modifyReservationSchema } from '../@types/cli-api/reservations/reservations/modify/schema';
import { ModifyReservationController } from '../controllers/reservations/reservations/controllers/ModifyReservationController';
import { FindRateController } from '../controllers/reservations/rates/controllers/FindRateController';

@singleton()
export class ReservationRoutes {
  constructor(
    private readonly findReservationController: FindReservationController,
    private readonly modifyReservationController: ModifyReservationController,
    private readonly searchZonesController: SearchZonesController,
    private readonly findZoneController: FindZoneController,
    private readonly findRateController: FindRateController,
    private readonly searchFormFieldsController: SearchFormFieldsController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/:activationCode', {
      schema: {
        tags: ['Reservations'],
        description: 'Endpoint to get a reservation by activation code and return form fields',
        ...findReservationSchema,
      },
      handler: this.findReservationController.handler.bind(this.findReservationController),
    });

    server.patch('/', {
      schema: {
        tags: ['Reservations'],
        description: 'Endpoint to patch a reservation and return a payment URL',
        ...modifyReservationSchema,
      },
      handler: this.modifyReservationController.handler.bind(this.modifyReservationController),
    });

    server.get('/zones', {
      schema: {
        tags: ['Zones'],
        description: 'Endpoint to get all zones',
        ...searchZonesSchema,
      },
      handler: this.searchZonesController.handler.bind(this.searchZonesController),
    });

    server.get('/zones/:zoneId', {
      schema: {
        tags: ['Zones'],
        description: 'Endpoint to get a zone by id',
        ...findZoneSchema,
      },
      handler: this.findZoneController.handler.bind(this.findZoneController),
    });

    server.get('/rates/:rateId', {
      schema: {
        tags: ['Rates'],
        description: 'Endpoint to get a rate by id',
        ...findRateSchema,
      },
      handler: this.findRateController.handler.bind(this.findRateController),
    });

    server.get('/form-fields/:organizationId', {
      schema: {
        tags: ['Form Fields'],
        description: 'Endpoint to get form fields by organization id',
        ...searchFormFieldsSchema,
      },
      handler: this.searchFormFieldsController.handler.bind(this.searchFormFieldsController),
    });
  }
}
