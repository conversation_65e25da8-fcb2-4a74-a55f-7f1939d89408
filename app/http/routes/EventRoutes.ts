import { singleton } from 'tsyringe';

import { findZonesWebImageSchema } from '@app/http/@types/cli-api/events/findZonesWebImage/schema';
import { FindEventZonesWebImageController } from '@app/http/controllers/events/controllers/FindEventZonesWebImageControllers';

import { findEventSchema } from '../@types/cli-api/events/find/schema';
import { findBookingSpaceAvailabilitySchema } from '../@types/cli-api/events/findBookingSpaceAvailability/schema';
import { findEventMetadataSchema } from '../@types/cli-api/events/findMetadata/schema';
import { registerEventPreregisterUserSchema } from '../@types/cli-api/events/registerPreregisterUser/schema';
import { searchBookingsSchema } from '../@types/cli-api/events/searchBookingZones/schema';
import { searchEventSchema } from '../@types/cli-api/events/searchEvents/schema';
import { searchGuestListsSchema } from '../@types/cli-api/events/searchGuestLists/schema';
import { searchTicketsTypesSchema } from '../@types/cli-api/events/searchTicketsTypes/schema';
import { eventVisitedSchema } from '../@types/cli-api/events/visited/schema';
import { EventVisitedController } from '../controllers/events/controllers/EventVisitedController';
import { FindBookingSpaceAvailabilityController } from '../controllers/events/controllers/FindBookingSpaceAvailabilityController';
import { FindEventController } from '../controllers/events/controllers/FindEventController';
import { FindEventMetadataController } from '../controllers/events/controllers/FindEventMetadataController';
import { RegisterEventPreregisterUserController } from '../controllers/events/controllers/RegisterEventPreregisterUserController';
import { SearchEventBookingZonesController } from '../controllers/events/controllers/SearchEventBookingZonesController';
import { SearchEventGuestListTypesController } from '../controllers/events/controllers/SearchEventGuestListTypesController';
import { SearchEventsController } from '../controllers/events/controllers/SearchEventsController';
import { SearchEventTicketTypesController } from '../controllers/events/controllers/SearchEventTicketTypesController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class EventRoutes {
  constructor(
    private readonly eventVisitedController: EventVisitedController,
    private readonly findController: FindEventController,
    private readonly searchTicketsTypesController: SearchEventTicketTypesController,
    private readonly searchBookingZonesController: SearchEventBookingZonesController,
    private readonly searchGuestListTypesController: SearchEventGuestListTypesController,
    private readonly searchEventsController: SearchEventsController,
    private readonly registerEventPreregisterUserController: RegisterEventPreregisterUserController,
    private readonly findEventZonesWebImageController: FindEventZonesWebImageController,
    private readonly findBookingSpaceAvailabilityController: FindBookingSpaceAvailabilityController,
    private readonly findMetadataController: FindEventMetadataController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/:idOrCode', {
      schema: {
        tags: ['Events'],
        description: 'Find a event by Id',
        ...findEventSchema,
      },
      handler: this.findController.handler.bind(this.findController),
    });

    server.get('/:idOrCode/tickets-types', {
      schema: {
        tags: ['Events'],
        description: 'Search for event ticket types by event Id',
        ...searchTicketsTypesSchema,
      },
      handler: this.searchTicketsTypesController.handler.bind(this.searchTicketsTypesController),
    });

    server.get('/:id/bookings-zones', {
      schema: {
        tags: ['Events'],
        description: 'Search for event bookings by event Id',
        ...searchBookingsSchema,
      },
      handler: this.searchBookingZonesController.handler.bind(this.searchBookingZonesController),
    });

    server.get('/:id/guestlist-types', {
      schema: {
        tags: ['Events'],
        description: 'Search for event guest lists by event Id',
        ...searchGuestListsSchema,
      },
      handler: this.searchGuestListTypesController.handler.bind(this.searchGuestListTypesController),
    });

    server.get('/', {
      schema: {
        tags: ['Events'],
        description: 'Endpoint where can find events by slug',
        ...searchEventSchema,
      },
      handler: this.searchEventsController.handler.bind(this.searchEventsController),
    });

    server.post('/:id/visited', {
      schema: {
        tags: ['Events'],
        description: 'Register when an event microsite has been visited',
        ...eventVisitedSchema,
      },
      handler: this.eventVisitedController.handler.bind(this.eventVisitedController),
    });

    server.post('/:id/preregister', {
      schema: {
        tags: ['Events'],
        description: 'Register a user for an event preregister',
        ...registerEventPreregisterUserSchema,
      },
      handler: this.registerEventPreregisterUserController.handler.bind(this.registerEventPreregisterUserController),
    });

    server.get('/:eventId/distribution-web-image', {
      schema: {
        tags: ['Events'],
        description: 'Get the web image for an event',
        ...findZonesWebImageSchema,
      },
      handler: this.findEventZonesWebImageController.handler.bind(this.findEventZonesWebImageController),
    });


    server.get('/reservation-availability', {
      schema: {
        tags: ['Events'],
        description: 'Process a reservation availability link',
        ...findBookingSpaceAvailabilitySchema,
      },
      handler: this.findBookingSpaceAvailabilityController.handler.bind(this.findBookingSpaceAvailabilityController),
    });
    server.get('/:idOrCode/metadata', {
      schema: {
        tags: ['Events'],
        description: 'Find a event metadata by Id',
        ...findEventMetadataSchema,
      },
      handler: this.findMetadataController.handler.bind(this.findMetadataController),

    });
  }
}
