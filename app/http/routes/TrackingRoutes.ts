import { singleton } from 'tsyringe';

import { trackingTypeSchema } from '../@types/cli-api/tracking/schema';
import { TrackingController } from '../controllers/tracking/TrackingController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class TrackingRoutes {
  constructor(
    private readonly trackingController: TrackingController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.post('/:channel', {
      schema: {
        tags: ['Trackings'],
        description: 'Tracking actions',
        ...trackingTypeSchema,
      },
      handler: this.trackingController.handler.bind(this.trackingController),
    });
  }
}
